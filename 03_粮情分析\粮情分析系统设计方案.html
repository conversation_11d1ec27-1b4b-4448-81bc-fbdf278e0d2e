<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>粮情分析系统设计方案</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .header {
            text-align: center;
            color: white;
            margin-bottom: 30px;
        }
        
        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        
        .header p {
            font-size: 1.2em;
            opacity: 0.9;
        }
        
        .dashboard {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .card {
            background: white;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }
        
        .card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 40px rgba(0,0,0,0.15);
        }
        
        .card h3 {
            color: #4a5568;
            margin-bottom: 15px;
            font-size: 1.3em;
            border-bottom: 2px solid #e2e8f0;
            padding-bottom: 10px;
        }
        
        .architecture-diagram {
            background: white;
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }
        
        .module {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 15px;
            margin: 10px;
            border-radius: 10px;
            text-align: center;
            display: inline-block;
            min-width: 200px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }
        
        .flow-arrow {
            text-align: center;
            font-size: 2em;
            color: #667eea;
            margin: 10px 0;
        }
        
        .feature-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
            margin-top: 20px;
        }
        
        .feature-item {
            background: #f7fafc;
            padding: 15px;
            border-radius: 8px;
            border-left: 4px solid #667eea;
        }
        
        .feature-item h4 {
            color: #2d3748;
            margin-bottom: 8px;
        }
        
        .feature-item p {
            color: #4a5568;
            font-size: 0.9em;
        }
        
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        
        .status-normal { background-color: #48bb78; }
        .status-warning { background-color: #ed8936; }
        .status-danger { background-color: #f56565; }
        
        .mockup-section {
            background: white;
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }
        
        .mockup-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-top: 20px;
        }
        
        .mockup-panel {
            background: #f8f9fa;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            padding: 20px;
            min-height: 200px;
        }
        
        .mockup-header {
            background: #495057;
            color: white;
            padding: 10px;
            margin: -20px -20px 15px -20px;
            border-radius: 6px 6px 0 0;
            font-weight: bold;
        }
        
        .temp-display {
            background: linear-gradient(45deg, #ff6b6b, #feca57);
            color: white;
            padding: 15px;
            border-radius: 8px;
            text-align: center;
            margin: 10px 0;
            font-size: 1.2em;
            font-weight: bold;
        }
        
        .alert-item {
            background: #fff5f5;
            border: 1px solid #fed7d7;
            border-radius: 6px;
            padding: 10px;
            margin: 8px 0;
            display: flex;
            align-items: center;
        }
        
        .btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 0.9em;
            transition: transform 0.2s ease;
        }
        
        .btn:hover {
            transform: scale(1.05);
        }
        
        .improvement-list {
            list-style: none;
            padding: 0;
        }
        
        .improvement-list li {
            background: #f0fff4;
            border-left: 4px solid #48bb78;
            padding: 12px;
            margin: 8px 0;
            border-radius: 0 6px 6px 0;
        }
        
        .improvement-list li::before {
            content: "✓";
            color: #48bb78;
            font-weight: bold;
            margin-right: 8px;
        }
        
        @media (max-width: 768px) {
            .mockup-grid {
                grid-template-columns: 1fr;
            }
            
            .header h1 {
                font-size: 2em;
            }
            
            .container {
                padding: 10px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🌾 智能粮情分析系统</h1>
            <p>基于AI的粮食仓储智能诊断与决策支持平台</p>
        </div>

        <div class="architecture-diagram">
            <h2 style="text-align: center; color: #2d3748; margin-bottom: 30px;">系统架构设计</h2>
            <div style="text-align: center;">
                <div class="module">
                    <h4>数据采集层</h4>
                    <p>传感器数据 | 环境数据 | 操作记录</p>
                </div>
                <div class="flow-arrow">↓</div>
                <div class="module">
                    <h4>特征工程引擎</h4>
                    <p>多维度特征计算 | 空间分析 | 时序处理</p>
                </div>
                <div class="flow-arrow">↓</div>
                <div class="module">
                    <h4>AI诊断模型</h4>
                    <p>多标签分类 | 异常检测 | 模式识别</p>
                </div>
                <div class="flow-arrow">↓</div>
                <div class="module">
                    <h4>专家知识库</h4>
                    <p>规则引擎 | 建议生成 | 操作指导</p>
                </div>
            </div>
        </div>

        <div class="dashboard">
            <div class="card">
                <h3>📊 核心功能模块</h3>
                <div class="feature-grid">
                    <div class="feature-item">
                        <h4>实时监控</h4>
                        <p>24/7粮情状态监控</p>
                    </div>
                    <div class="feature-item">
                        <h4>智能诊断</h4>
                        <p>AI驱动的异常检测</p>
                    </div>
                    <div class="feature-item">
                        <h4>预警告警</h4>
                        <p>多级别告警机制</p>
                    </div>
                    <div class="feature-item">
                        <h4>决策支持</h4>
                        <p>专家建议生成</p>
                    </div>
                </div>
            </div>

            <div class="card">
                <h3>🔧 技术特色</h3>
                <ul class="improvement-list">
                    <li>多维度特征工程：点线面体全方位分析</li>
                    <li>AI多标签分类：18种诊断类型识别</li>
                    <li>专家知识融合：规则与AI结合</li>
                    <li>实时流式处理：毫秒级响应</li>
                    <li>可视化展示：3D仓房建模</li>
                </ul>
            </div>

            <div class="card">
                <h3>⚡ 系统优势</h3>
                <ul class="improvement-list">
                    <li>从预测转向诊断，更贴近实际需求</li>
                    <li>多层次聚合分析，全面覆盖粮情</li>
                    <li>可解释AI模型，决策透明可信</li>
                    <li>微服务架构，高可用高扩展</li>
                    <li>移动端支持，随时随地监控</li>
                </ul>
            </div>
        </div>

        <div class="mockup-section">
            <h2 style="color: #2d3748; margin-bottom: 20px;">🖥️ 用户界面设计预览</h2>
            <div class="mockup-grid">
                <div class="mockup-panel">
                    <div class="mockup-header">粮情监控大屏</div>
                    <div class="temp-display">
                        <span class="status-indicator status-normal"></span>
                        A仓平均温度: 15.2°C
                    </div>
                    <div class="temp-display">
                        <span class="status-indicator status-warning"></span>
                        B仓平均温度: 23.8°C
                    </div>
                    <div class="temp-display">
                        <span class="status-indicator status-danger"></span>
                        C仓平均温度: 31.5°C
                    </div>
                    <button class="btn">查看详细分析</button>
                </div>

                <div class="mockup-panel">
                    <div class="mockup-header">智能告警中心</div>
                    <div class="alert-item">
                        <span class="status-indicator status-danger"></span>
                        <div>
                            <strong>C仓表层局部发热</strong><br>
                            <small>建议立即检查仓顶密封性</small>
                        </div>
                    </div>
                    <div class="alert-item">
                        <span class="status-indicator status-warning"></span>
                        <div>
                            <strong>B仓温升速率异常</strong><br>
                            <small>建议启动夜间通风</small>
                        </div>
                    </div>
                    <button class="btn">处理告警</button>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 简单的交互效果
        document.addEventListener('DOMContentLoaded', function() {
            const cards = document.querySelectorAll('.card');
            cards.forEach(card => {
                card.addEventListener('click', function() {
                    this.style.transform = 'scale(1.02)';
                    setTimeout(() => {
                        this.style.transform = '';
                    }, 200);
                });
            });

            // 模拟实时数据更新
            setInterval(() => {
                const tempDisplays = document.querySelectorAll('.temp-display');
                tempDisplays.forEach(display => {
                    const currentTemp = parseFloat(display.textContent.match(/\d+\.\d+/)[0]);
                    const newTemp = (currentTemp + (Math.random() - 0.5) * 0.2).toFixed(1);
                    display.innerHTML = display.innerHTML.replace(/\d+\.\d+/, newTemp);
                });
            }, 3000);
        });
    </script>
</body>
</html>
