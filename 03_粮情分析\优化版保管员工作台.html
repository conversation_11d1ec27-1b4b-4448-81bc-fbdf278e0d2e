<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>粮情分析系统 - 优化版保管员工作台</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: #f5f7fa;
            color: #333;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 15px 20px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        .header h1 {
            font-size: 1.8em;
            margin-bottom: 5px;
        }
        
        .quick-status {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 8px;
            padding: 12px 20px;
            margin: 20px;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }
        
        .silo-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 20px;
            padding: 20px;
        }
        
        .silo-card {
            background: white;
            border-radius: 12px;
            padding: 20px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
            cursor: pointer;
            border: 2px solid transparent;
        }
        
        .silo-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        }
        
        .silo-card.selected {
            border-color: #667eea;
        }
        
        .silo-card.critical {
            border-left: 4px solid #d63031;
            background: #fff0f0;
            animation: pulse 2s infinite;
        }
        
        @keyframes pulse {
            0% { box-shadow: 0 4px 12px rgba(0,0,0,0.1); }
            50% { box-shadow: 0 8px 25px rgba(214, 48, 49, 0.3); }
            100% { box-shadow: 0 4px 12px rgba(0,0,0,0.1); }
        }
        
        /* 异常详情面板样式 */
        .detail-panel {
            background: white;
            margin: 20px;
            border-radius: 12px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .detail-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            font-size: 1.2em;
            font-weight: bold;
        }
        
        /* 第一层：异常总述 */
        .anomaly-summary {
            padding: 25px;
            background: linear-gradient(135deg, #fff5f5 0%, #fff0f0 100%);
            border-bottom: 3px solid #e74c3c;
        }
        
        .summary-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }
        
        .urgency-badge {
            background: #e74c3c;
            color: white;
            padding: 8px 16px;
            border-radius: 20px;
            font-weight: bold;
            font-size: 0.9em;
            animation: blink 1.5s infinite;
        }
        
        @keyframes blink {
            0%, 50% { opacity: 1; }
            51%, 100% { opacity: 0.7; }
        }
        
        .summary-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 20px;
        }
        
        .stat-card {
            background: white;
            padding: 15px;
            border-radius: 8px;
            text-align: center;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        .stat-number {
            font-size: 2em;
            font-weight: bold;
            color: #e74c3c;
        }
        
        .priority-actions {
            background: white;
            padding: 15px;
            border-radius: 8px;
            border-left: 4px solid #f39c12;
        }
        
        .priority-item {
            display: flex;
            align-items: center;
            padding: 8px 0;
            border-bottom: 1px solid #eee;
        }
        
        .priority-number {
            background: #f39c12;
            color: white;
            width: 24px;
            height: 24px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            margin-right: 12px;
            font-size: 0.8em;
        }
        
        /* 第二层：异常分类展示 */
        .anomaly-groups {
            padding: 25px;
        }
        
        .group-card {
            background: #f8f9fa;
            border-radius: 10px;
            margin-bottom: 20px;
            overflow: hidden;
            border: 1px solid #dee2e6;
        }
        
        .group-header {
            background: #495057;
            color: white;
            padding: 15px 20px;
            cursor: pointer;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .group-header:hover {
            background: #343a40;
        }
        
        .group-stats {
            display: flex;
            gap: 15px;
        }
        
        .group-stat {
            background: rgba(255,255,255,0.2);
            padding: 4px 12px;
            border-radius: 15px;
            font-size: 0.8em;
        }
        
        .group-content {
            padding: 20px;
            display: none;
        }
        
        .group-content.expanded {
            display: block;
        }
        
        .group-principle {
            background: #e3f2fd;
            padding: 15px;
            border-radius: 6px;
            margin-bottom: 15px;
            border-left: 4px solid #2196f3;
        }
        
        /* 第三层：单项异常详情 */
        .anomaly-item {
            background: white;
            border-radius: 8px;
            margin-bottom: 15px;
            border: 1px solid #ddd;
            overflow: hidden;
        }
        
        .anomaly-header {
            background: #fff3e0;
            padding: 12px 15px;
            border-bottom: 1px solid #ddd;
            cursor: pointer;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .anomaly-title {
            font-weight: bold;
            color: #e65100;
        }
        
        .confidence-badge {
            background: #4caf50;
            color: white;
            padding: 2px 8px;
            border-radius: 10px;
            font-size: 0.7em;
        }
        
        .anomaly-details {
            padding: 15px;
            display: none;
        }
        
        .anomaly-details.expanded {
            display: block;
        }
        
        .detail-section {
            margin-bottom: 20px;
        }
        
        .detail-section h4 {
            color: #495057;
            margin-bottom: 10px;
            padding-bottom: 5px;
            border-bottom: 2px solid #e9ecef;
        }
        
        .evidence-table {
            width: 100%;
            border-collapse: collapse;
            margin: 10px 0;
        }
        
        .evidence-table th,
        .evidence-table td {
            padding: 8px 12px;
            border: 1px solid #ddd;
            text-align: left;
        }
        
        .evidence-table th {
            background: #f8f9fa;
            font-weight: bold;
        }
        
        .exceeded {
            color: #d32f2f;
            font-weight: bold;
        }
        
        .action-steps {
            background: #f0f8ff;
            padding: 15px;
            border-radius: 6px;
            border-left: 4px solid #2196f3;
        }
        
        .action-step {
            margin: 8px 0;
            padding-left: 20px;
            position: relative;
        }
        
        .action-step::before {
            content: "▶";
            position: absolute;
            left: 0;
            color: #2196f3;
        }
        
        .resource-info {
            background: #fff8e1;
            padding: 15px;
            border-radius: 6px;
            margin-top: 15px;
        }
        
        .btn {
            background: #667eea;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 8px;
            margin-top: 8px;
        }
        
        .btn:hover {
            background: #5a6fd8;
        }
        
        .btn-success {
            background: #28a745;
        }
        
        .btn-warning {
            background: #ffc107;
            color: #333;
        }
        
        .hidden {
            display: none;
        }
        
        .expand-icon {
            transition: transform 0.3s ease;
        }
        
        .expand-icon.rotated {
            transform: rotate(90deg);
        }
        
        @media (max-width: 768px) {
            .silo-grid {
                grid-template-columns: 1fr;
                padding: 10px;
            }
            
            .summary-stats {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🌾 粮情分析系统 - 优化版保管员工作台</h1>
        <div class="subtitle">总-分-据 层次化异常诊断</div>
    </div>

    <div class="quick-status">
        <div class="message">
            ⚠️ 当前有 3 个仓房需要立即关注：C仓(紧急)、B仓(危险)、D仓(警告)
        </div>
        <button class="btn" onclick="scrollToAbnormal()">立即查看</button>
    </div>

    <div class="silo-grid" id="siloGrid">
        <!-- 仓房状态卡片 -->
    </div>

    <div class="detail-panel hidden" id="detailPanel">
        <div class="detail-header" id="detailHeader">
            C仓 - 异常诊断详情
        </div>
        
        <!-- 第一层：异常总述 -->
        <div class="anomaly-summary">
            <div class="summary-header">
                <h2>🚨 异常总述</h2>
                <div class="urgency-badge">需立即处理</div>
            </div>
            
            <div class="summary-stats">
                <div class="stat-card">
                    <div class="stat-number">4</div>
                    <div>异常总数</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">2</div>
                    <div>紧急异常</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">1</div>
                    <div>高风险异常</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">2-4h</div>
                    <div>预估处理时间</div>
                </div>
            </div>
            
            <div class="priority-actions">
                <h4>🎯 优先级处理建议</h4>
                <div class="priority-item">
                    <div class="priority-number">1</div>
                    <div><strong>立即处理：</strong>表层局部发热 - 检查仓顶密封性，进行感官检查</div>
                </div>
                <div class="priority-item">
                    <div class="priority-number">2</div>
                    <div><strong>紧急处理：</strong>温升速率异常 - 启动通风系统降温</div>
                </div>
                <div class="priority-item">
                    <div class="priority-number">3</div>
                    <div><strong>密切关注：</strong>仓内温差过大 - 优化通风策略</div>
                </div>
                <div class="priority-item">
                    <div class="priority-number">4</div>
                    <div><strong>计划处理：</strong>传感器异常 - 安排检修更换</div>
                </div>
            </div>
            
            <div class="resource-info">
                <h4>📋 所需资源</h4>
                <p><strong>人员：</strong>保管员2人、维修工1人</p>
                <p><strong>设备：</strong>通风设备、检测仪器、维修工具</p>
                <p><strong>预估成本：</strong>500-1000元</p>
            </div>
        </div>
        
        <!-- 第二层：异常分类展示 -->
        <div class="anomaly-groups">
            <h2>📊 异常分类详情</h2>
            
            <!-- 温度异常组 -->
            <div class="group-card">
                <div class="group-header" onclick="toggleGroup('temperature')">
                    <div>
                        <span class="expand-icon" id="temperature-icon">▶</span>
                        🌡️ 温度异常组
                    </div>
                    <div class="group-stats">
                        <div class="group-stat">2个异常</div>
                        <div class="group-stat">紧急处理</div>
                    </div>
                </div>
                <div class="group-content" id="temperature-content">
                    <div class="group-principle">
                        <strong>处理原则：</strong>立即通风降温，监控温度变化，必要时进行品质检测
                    </div>
                    
                    <!-- 表层局部发热 -->
                    <div class="anomaly-item">
                        <div class="anomaly-header" onclick="toggleAnomaly('surface-heating')">
                            <div class="anomaly-title">表层局部发热</div>
                            <div>
                                <span class="confidence-badge">置信度: 87.3%</span>
                                <span class="expand-icon" id="surface-heating-icon">▶</span>
                            </div>
                        </div>
                        <div class="anomaly-details" id="surface-heating-details">
                            <div class="detail-section">
                                <h4>🔍 异常描述</h4>
                                <p>检测到C仓表层东北角区域温度异常升高，存在明显的局部发热现象，最高温度达到32.8°C，超出安全阈值。</p>
                                <p><strong>影响范围：</strong>表层东北角区域（约15平方米）</p>
                            </div>
                            
                            <div class="detail-section">
                                <h4>📊 判定依据</h4>
                                <table class="evidence-table">
                                    <thead>
                                        <tr>
                                            <th>特征指标</th>
                                            <th>当前值</th>
                                            <th>安全阈值</th>
                                            <th>状态</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td>区域最高温度</td>
                                            <td class="exceeded">32.8°C</td>
                                            <td>< 25°C</td>
                                            <td class="exceeded">超标</td>
                                        </tr>
                                        <tr>
                                            <td>区域温度标准差</td>
                                            <td class="exceeded">4.2°C</td>
                                            <td>< 2.0°C</td>
                                            <td class="exceeded">超标</td>
                                        </tr>
                                        <tr>
                                            <td>24小时温升</td>
                                            <td class="exceeded">3.1°C</td>
                                            <td>< 1.0°C</td>
                                            <td class="exceeded">超标</td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                            
                            <div class="detail-section">
                                <h4>🛠️ 作业建议</h4>
                                <div class="action-steps">
                                    <div class="action-step">立即检查仓顶密封性，重点检查东北角区域</div>
                                    <div class="action-step">进仓进行感官检查，观察粮面有无异味、霉变、结块</div>
                                    <div class="action-step">如情况轻微，启动仓顶风机进行水平通风</div>
                                    <div class="action-step">如发现严重问题，立即准备局部扒粮处理</div>
                                    <div class="action-step">每2小时监测一次温度变化</div>
                                </div>
                            </div>
                            
                            <div class="detail-section">
                                <h4>📈 预期效果</h4>
                                <p>执行建议后，预计6-12小时内该区域温度下降2-3°C，24小时内恢复到安全范围。</p>
                            </div>
                            
                            <div class="detail-section">
                                <button class="btn btn-success">✅ 执行建议</button>
                                <button class="btn">📊 查看温度阵列</button>
                                <button class="btn">📈 查看趋势图</button>
                                <button class="btn btn-warning">📝 记录处理结果</button>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 温升速率异常 -->
                    <div class="anomaly-item">
                        <div class="anomaly-header" onclick="toggleAnomaly('temp-rise')">
                            <div class="anomaly-title">温升速率异常</div>
                            <div>
                                <span class="confidence-badge">置信度: 92.1%</span>
                                <span class="expand-icon" id="temp-rise-icon">▶</span>
                            </div>
                        </div>
                        <div class="anomaly-details" id="temp-rise-details">
                            <div class="detail-section">
                                <h4>🔍 异常描述</h4>
                                <p>检测到C仓整体温度上升速率异常，24小时内平均温度上升2.8°C，远超正常波动范围。</p>
                            </div>
                            
                            <div class="detail-section">
                                <h4>🛠️ 作业建议</h4>
                                <div class="action-steps">
                                    <div class="action-step">立即启动机械通风系统</div>
                                    <div class="action-step">检查通风道是否畅通</div>
                                    <div class="action-step">调整通风策略，增加通风频次</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 分布异常组 -->
            <div class="group-card">
                <div class="group-header" onclick="toggleGroup('distribution')">
                    <div>
                        <span class="expand-icon" id="distribution-icon">▶</span>
                        📐 分布异常组
                    </div>
                    <div class="group-stats">
                        <div class="group-stat">1个异常</div>
                        <div class="group-stat">密切关注</div>
                    </div>
                </div>
                <div class="group-content" id="distribution-content">
                    <div class="group-principle">
                        <strong>处理原则：</strong>优化通风策略，均衡温度分布，改善储存环境
                    </div>
                    
                    <div class="anomaly-item">
                        <div class="anomaly-header" onclick="toggleAnomaly('temp-diff')">
                            <div class="anomaly-title">仓内温差过大</div>
                            <div>
                                <span class="confidence-badge">置信度: 78.5%</span>
                                <span class="expand-icon" id="temp-diff-icon">▶</span>
                            </div>
                        </div>
                        <div class="anomaly-details" id="temp-diff-details">
                            <div class="detail-section">
                                <h4>🔍 异常描述</h4>
                                <p>仓内不同区域温度差异过大，最大温差达到8.2°C，可能影响粮食储存均匀性。</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 硬件异常组 -->
            <div class="group-card">
                <div class="group-header" onclick="toggleGroup('hardware')">
                    <div>
                        <span class="expand-icon" id="hardware-icon">▶</span>
                        🔧 硬件异常组
                    </div>
                    <div class="group-stats">
                        <div class="group-stat">1个异常</div>
                        <div class="group-stat">计划处理</div>
                    </div>
                </div>
                <div class="group-content" id="hardware-content">
                    <div class="group-principle">
                        <strong>处理原则：</strong>标记异常传感器，安排检修计划，加强人工巡检
                    </div>
                    
                    <div class="anomaly-item">
                        <div class="anomaly-header" onclick="toggleAnomaly('sensor-fault')">
                            <div class="anomaly-title">传感器异常</div>
                            <div>
                                <span class="confidence-badge">置信度: 95.2%</span>
                                <span class="expand-icon" id="sensor-fault-icon">▶</span>
                            </div>
                        </div>
                        <div class="anomaly-details" id="sensor-fault-details">
                            <div class="detail-section">
                                <h4>🔍 异常描述</h4>
                                <p>传感器T-C-15数据波动异常，可能存在硬件故障。</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 模拟仓房数据
        const siloData = [
            {
                id: 'A',
                name: 'A仓',
                status: 'normal',
                maxTemp: 18.5,
                avgTemp: 16.2,
                alertCount: 0,
                icon: '✅'
            },
            {
                id: 'B',
                name: 'B仓',
                status: 'danger',
                maxTemp: 28.3,
                avgTemp: 24.1,
                alertCount: 2,
                icon: '⚠️'
            },
            {
                id: 'C',
                name: 'C仓',
                status: 'critical',
                maxTemp: 32.8,
                avgTemp: 29.5,
                alertCount: 4,
                icon: '🚨'
            },
            {
                id: 'D',
                name: 'D仓',
                status: 'warning',
                maxTemp: 22.1,
                avgTemp: 19.8,
                alertCount: 1,
                icon: '⚡'
            }
        ];

        let selectedSilo = null;

        // 生成仓房卡片
        function generateSiloCards() {
            const grid = document.getElementById('siloGrid');
            grid.innerHTML = '';

            siloData.forEach(silo => {
                const card = document.createElement('div');
                card.className = `silo-card ${silo.status}`;
                card.onclick = () => selectSilo(silo.id);

                card.innerHTML = `
                    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 15px;">
                        <div style="display: flex; align-items: center;">
                            <span style="font-size: 1.5em; margin-right: 8px;">${silo.icon}</span>
                            <strong style="font-size: 1.3em;">${silo.name}</strong>
                        </div>
                        <span style="padding: 4px 12px; border-radius: 20px; font-size: 0.8em; background: ${getStatusColor(silo.status)}; color: white;">
                            ${getStatusText(silo.status)}
                        </span>
                    </div>
                    <div style="margin: 15px 0;">
                        <div style="display: flex; justify-content: space-between; margin: 8px 0;">
                            <span style="color: #666;">最高温度:</span>
                            <span style="font-weight: bold; ${silo.maxTemp > 30 ? 'color: #d63031;' : ''}">${silo.maxTemp}°C</span>
                        </div>
                        <div style="display: flex; justify-content: space-between; margin: 8px 0;">
                            <span style="color: #666;">平均温度:</span>
                            <span style="font-weight: bold;">${silo.avgTemp}°C</span>
                        </div>
                        ${silo.alertCount > 0 ? `
                            <div style="background: #ff7675; color: white; padding: 4px 8px; border-radius: 12px; font-size: 0.8em; display: inline-block; margin-top: 8px;">
                                ${silo.alertCount} 个异常项目
                            </div>
                        ` : ''}
                    </div>
                    ${silo.status !== 'normal' ? `
                        <button style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; border: none; padding: 10px 20px; border-radius: 6px; cursor: pointer; width: 100%; margin-top: 12px;">
                            👁️ 查看详细诊断
                        </button>
                    ` : ''}
                `;

                grid.appendChild(card);
            });
        }

        function getStatusColor(status) {
            const colorMap = {
                'normal': '#00b894',
                'warning': '#fdcb6e',
                'danger': '#e17055',
                'critical': '#d63031'
            };
            return colorMap[status] || '#74b9ff';
        }

        function getStatusText(status) {
            const statusMap = {
                'normal': '正常',
                'warning': '警告',
                'danger': '危险',
                'critical': '紧急'
            };
            return statusMap[status] || '未知';
        }

        function selectSilo(siloId) {
            selectedSilo = siloId;
            
            // 更新卡片选中状态
            document.querySelectorAll('.silo-card').forEach(card => {
                card.classList.remove('selected');
            });
            event.currentTarget.classList.add('selected');

            // 显示详细面板
            showDetailPanel(siloId);
        }

        function showDetailPanel(siloId) {
            const panel = document.getElementById('detailPanel');
            const header = document.getElementById('detailHeader');
            
            header.textContent = `${siloId}仓 - 异常诊断详情`;
            panel.classList.remove('hidden');
            
            // 滚动到详细面板
            panel.scrollIntoView({ behavior: 'smooth' });
        }

        function toggleGroup(groupId) {
            const content = document.getElementById(`${groupId}-content`);
            const icon = document.getElementById(`${groupId}-icon`);
            
            content.classList.toggle('expanded');
            icon.classList.toggle('rotated');
        }

        function toggleAnomaly(anomalyId) {
            const details = document.getElementById(`${anomalyId}-details`);
            const icon = document.getElementById(`${anomalyId}-icon`);
            
            details.classList.toggle('expanded');
            icon.classList.toggle('rotated');
        }

        function scrollToAbnormal() {
            const abnormalCards = document.querySelectorAll('.silo-card.danger, .silo-card.critical, .silo-card.warning');
            if (abnormalCards.length > 0) {
                abnormalCards[0].scrollIntoView({ behavior: 'smooth' });
                abnormalCards[0].click();
            }
        }

        // 初始化页面
        generateSiloCards();

        // 自动选择C仓进行演示
        setTimeout(() => {
            const cSiloCard = document.querySelector('.silo-card.critical');
            if (cSiloCard) {
                cSiloCard.click();
            }
        }, 1000);
    </script>
</body>
</html>
