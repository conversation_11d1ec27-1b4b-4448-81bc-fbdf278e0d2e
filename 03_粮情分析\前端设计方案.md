# 粮情分析系统前端设计方案（保管员视角）

## 设计理念：以保管员工作流程为核心

### 保管员核心需求分析
1. **快速识别异常**：一眼看出哪些仓有问题，问题严重程度
2. **详细诊断信息**：点击异常仓房，立即查看具体异常类型、位置、严重程度
3. **可信的作业建议**：基于AI诊断提供具体可执行的操作建议
4. **判定依据透明**：能看到AI是基于什么数据和特征做出的判断
5. **数据对照验证**：查看粮温阵列和报表，人工验证AI建议的合理性
6. **操作记录追踪**：记录执行的操作和效果，形成经验积累

## 1. 技术架构

### 1.1 技术栈选择
```json
{
  "框架": "React 18 + TypeScript",
  "UI组件库": "Ant Design 5.x",
  "状态管理": "Redux Toolkit + RTK Query",
  "路由": "React Router 6",
  "图表库": "ECharts + D3.js",
  "3D可视化": "Three.js",
  "地图": "Mapbox GL JS",
  "实时通信": "Socket.IO",
  "构建工具": "Vite",
  "代码质量": "ESLint + Prettier + Husky"
}
```

### 1.2 项目结构
```
src/
├── components/          # 通用组件
│   ├── Charts/         # 图表组件
│   ├── Layout/         # 布局组件
│   ├── Visualization/  # 可视化组件
│   └── Common/         # 通用UI组件
├── pages/              # 页面组件
│   ├── Dashboard/      # 仪表板
│   ├── Monitoring/     # 监控页面
│   ├── Analysis/       # 分析页面
│   ├── Alerts/         # 告警页面
│   └── Settings/       # 设置页面
├── hooks/              # 自定义Hooks
├── services/           # API服务
├── store/              # 状态管理
├── utils/              # 工具函数
├── types/              # TypeScript类型定义
└── assets/             # 静态资源
```

## 2. 保管员工作台设计

### 2.1 主工作台 - 异常快速识别界面

#### 2.1.1 核心布局设计
```tsx
// KeeperWorkbench.tsx - 保管员主工作台
import React, { useState } from 'react';
import { Row, Col, Card, Badge, Button, Tag, Alert } from 'antd';
import { ExclamationCircleOutlined, CheckCircleOutlined, EyeOutlined } from '@ant-design/icons';

interface SiloStatus {
  id: string;
  name: string;
  status: 'normal' | 'warning' | 'danger' | 'critical';
  alertCount: number;
  maxTemp: number;
  avgTemp: number;
  lastUpdate: string;
  alerts: AlertInfo[];
}

const KeeperWorkbench: React.FC = () => {
  const [selectedSilo, setSelectedSilo] = useState<string | null>(null);

  return (
    <div className="keeper-workbench">
      {/* 顶部快速状态栏 */}
      <div className="quick-status-bar">
        <Alert
          message="当前有 3 个仓房需要立即关注"
          type="warning"
          showIcon
          style={{ marginBottom: 16 }}
          action={
            <Button size="small" type="primary">
              查看详情
            </Button>
          }
        />
      </div>

      {/* 仓房状态网格 - 保管员最关心的核心区域 */}
      <div className="silo-grid">
        <Row gutter={[16, 16]}>
          {siloData.map(silo => (
            <Col span={6} key={silo.id}>
              <SiloStatusCard
                silo={silo}
                onClick={() => setSelectedSilo(silo.id)}
                isSelected={selectedSilo === silo.id}
              />
            </Col>
          ))}
        </Row>
      </div>

      {/* 选中仓房的详细信息 */}
      {selectedSilo && (
        <SiloDetailPanel siloId={selectedSilo} />
      )}
    </div>
  );
};

// 仓房状态卡片 - 保管员一眼识别异常的关键组件
const SiloStatusCard: React.FC<{
  silo: SiloStatus;
  onClick: () => void;
  isSelected: boolean;
}> = ({ silo, onClick, isSelected }) => {
  const getStatusConfig = (status: string) => {
    switch (status) {
      case 'critical':
        return { color: '#ff4d4f', bgColor: '#fff2f0', text: '紧急', icon: '🚨' };
      case 'danger':
        return { color: '#fa8c16', bgColor: '#fff7e6', text: '危险', icon: '⚠️' };
      case 'warning':
        return { color: '#faad14', bgColor: '#fffbe6', text: '警告', icon: '⚡' };
      default:
        return { color: '#52c41a', bgColor: '#f6ffed', text: '正常', icon: '✅' };
    }
  };

  const statusConfig = getStatusConfig(silo.status);

  return (
    <Card
      className={`silo-status-card ${isSelected ? 'selected' : ''}`}
      onClick={onClick}
      style={{
        borderColor: statusConfig.color,
        backgroundColor: statusConfig.bgColor,
        cursor: 'pointer',
        borderWidth: isSelected ? 3 : 1,
      }}
      hoverable
    >
      <div className="silo-header">
        <div className="silo-name">
          <span className="silo-icon">{statusConfig.icon}</span>
          <strong>{silo.name}</strong>
        </div>
        <Tag color={statusConfig.color}>{statusConfig.text}</Tag>
      </div>

      <div className="silo-metrics">
        <div className="metric">
          <span className="label">最高温度:</span>
          <span className={`value ${silo.maxTemp > 30 ? 'danger' : ''}`}>
            {silo.maxTemp}°C
          </span>
        </div>
        <div className="metric">
          <span className="label">平均温度:</span>
          <span className="value">{silo.avgTemp}°C</span>
        </div>
        {silo.alertCount > 0 && (
          <div className="alert-count">
            <Badge count={silo.alertCount} style={{ backgroundColor: statusConfig.color }}>
              <span>异常项目</span>
            </Badge>
          </div>
        )}
      </div>

      <div className="last-update">
        更新时间: {silo.lastUpdate}
      </div>

      {silo.status !== 'normal' && (
        <Button
          type="primary"
          size="small"
          icon={<EyeOutlined />}
          style={{ marginTop: 8, width: '100%' }}
        >
          查看详细诊断
        </Button>
      )}
    </Card>
  );
};
```

### 2.2 单仓详细诊断界面 - "总-分-据"层次化架构

#### 2.2.1 信息架构设计原则

基于保管员的认知习惯和决策流程，采用"总-分-据"三层信息架构：

1. **总述层**：快速了解整体异常情况和处理优先级
2. **分类层**：按异常性质分组，理解处理原则和策略
3. **详据层**：深入单项异常的完整论证和操作指南

#### 2.2.2 异常分类体系

根据异常的性质、处理方式和紧急程度，将异常分为4个主要分组：

```typescript
// 异常分组定义
interface AnomalyGrouping {
  hardware: {
    name: '硬件异常组';
    types: ['硬件异常-点位', '硬件异常-线缆'];
    urgency: 'low-medium';
    principle: '标记异常、安排检修、加强人工巡检';
  };
  temperature: {
    name: '温度异常组';
    types: ['温升速率异常', '超最高温', '整仓发热'];
    urgency: 'medium-high';
    principle: '立即通风、温度控制、品质检测';
  };
  spatial: {
    name: '空间异常组';
    types: ['表层发热', '中部发热', '底部发热', '垂直发热'];
    urgency: 'medium-high';
    principle: '区域检查、局部处理、防止扩散';
  };
  distribution: {
    name: '分布异常组';
    types: ['同层不均', '外圈过高', '温差过大', '新粮后熟'];
    urgency: 'low-medium';
    principle: '优化通风、均衡温度、改善环境';
  };
}
```

#### 2.2.3 第一层：异常总述设计
```tsx
// AnomalySummaryLayer.tsx - 第一层：异常总述
import React from 'react';
import { Card, Row, Col, Badge, Timeline } from 'antd';
import { ExclamationCircleOutlined, ClockCircleOutlined } from '@ant-design/icons';

interface AnomalySummaryProps {
  siloId: string;
  anomalies: AnomalyData[];
}

const AnomalySummaryLayer: React.FC<AnomalySummaryProps> = ({ siloId, anomalies }) => {
  const summaryData = calculateSummary(anomalies);

  return (
    <Card
      className="anomaly-summary-layer"
      style={{
        background: 'linear-gradient(135deg, #fff5f5 0%, #fff0f0 100%)',
        borderLeft: '4px solid #e74c3c'
      }}
    >
      <div className="summary-header">
        <h2>🚨 异常总述</h2>
        <Badge
          count={`需${summaryData.urgencyLevel}处理`}
          style={{
            backgroundColor: getUrgencyColor(summaryData.urgencyLevel),
            animation: summaryData.urgencyLevel === '立即' ? 'blink 1.5s infinite' : 'none'
          }}
        />
      </div>

      {/* 统计概览 */}
      <Row gutter={16} className="summary-stats">
        <Col span={6}>
          <div className="stat-card">
            <div className="stat-number">{summaryData.totalCount}</div>
            <div className="stat-label">异常总数</div>
          </div>
        </Col>
        <Col span={6}>
          <div className="stat-card">
            <div className="stat-number critical">{summaryData.criticalCount}</div>
            <div className="stat-label">紧急异常</div>
          </div>
        </Col>
        <Col span={6}>
          <div className="stat-card">
            <div className="stat-number high">{summaryData.highCount}</div>
            <div className="stat-label">高风险异常</div>
          </div>
        </Col>
        <Col span={6}>
          <div className="stat-card">
            <div className="stat-number">{summaryData.estimatedTime}</div>
            <div className="stat-label">预估处理时间</div>
          </div>
        </Col>
      </Row>

      {/* 优先级处理建议 */}
      <Card title="🎯 优先级处理建议" size="small" style={{ marginTop: 16 }}>
        <Timeline>
          {summaryData.prioritizedActions.map((action, index) => (
            <Timeline.Item
              key={index}
              dot={<ExclamationCircleOutlined style={{ color: action.urgencyColor }} />}
              color={action.urgencyColor}
            >
              <div className="priority-action">
                <Badge count={index + 1} style={{ backgroundColor: '#f39c12', marginRight: 8 }} />
                <strong>{action.urgencyText}：</strong>
                {action.description}
              </div>
            </Timeline.Item>
          ))}
        </Timeline>
      </Card>

      {/* 资源需求 */}
      <Card title="📋 所需资源" size="small" style={{ marginTop: 16 }}>
        <Row gutter={16}>
          <Col span={8}>
            <div><strong>人员：</strong>{summaryData.resources.personnel.join('、')}</div>
          </Col>
          <Col span={8}>
            <div><strong>设备：</strong>{summaryData.resources.equipment.join('、')}</div>
          </Col>
          <Col span={8}>
            <div><strong>预估成本：</strong>{summaryData.resources.estimatedCost}</div>
          </Col>
        </Row>
      </Card>
    </Card>
  );
};

// 计算总述数据
function calculateSummary(anomalies: AnomalyData[]) {
  const criticalCount = anomalies.filter(a => a.severity === 'critical').length;
  const highCount = anomalies.filter(a => a.severity === 'high').length;

  // 根据异常严重程度确定整体紧急程度
  const urgencyLevel = criticalCount > 0 ? '立即' :
                      highCount > 0 ? '紧急' :
                      anomalies.length > 0 ? '密切关注' : '建议';

  // 生成优先级处理建议
  const prioritizedActions = generatePriorityActions(anomalies);

  // 计算资源需求
  const resources = calculateResourceRequirements(anomalies);

  return {
    totalCount: anomalies.length,
    criticalCount,
    highCount,
    urgencyLevel,
    estimatedTime: calculateEstimatedTime(anomalies),
    prioritizedActions,
    resources
  };
}
```

#### 2.2.4 第二层：异常分类展示设计

```tsx
// AnomalyGroupLayer.tsx - 第二层：异常分类展示
import React, { useState } from 'react';
import { Card, Collapse, Badge, Tag } from 'antd';
import { CaretRightOutlined } from '@ant-design/icons';

const { Panel } = Collapse;

interface AnomalyGroupLayerProps {
  anomalies: AnomalyData[];
}

const AnomalyGroupLayer: React.FC<AnomalyGroupLayerProps> = ({ anomalies }) => {
  const groupedAnomalies = groupAnomaliesByType(anomalies);

  return (
    <div className="anomaly-group-layer">
      <h2>📊 异常分类详情</h2>

      <Collapse
        expandIcon={({ isActive }) => <CaretRightOutlined rotate={isActive ? 90 : 0} />}
        className="anomaly-groups"
      >
        {Object.entries(groupedAnomalies).map(([groupType, groupData]) => (
          <Panel
            key={groupType}
            header={
              <div className="group-header">
                <span className="group-icon">{getGroupIcon(groupType)}</span>
                <span className="group-name">{groupData.name}</span>
                <div className="group-stats">
                  <Badge count={groupData.anomalies.length} style={{ backgroundColor: '#52c41a' }} />
                  <Tag color={getUrgencyColor(groupData.urgency)}>{groupData.urgencyText}</Tag>
                </div>
              </div>
            }
            className={`group-panel ${groupType}`}
          >
            {/* 分组处理原则 */}
            <Card size="small" className="group-principle">
              <div><strong>处理原则：</strong>{groupData.principle}</div>
              <div><strong>预估时间：</strong>{groupData.estimatedTime}</div>
            </Card>

            {/* 该组内的异常列表 */}
            <div className="group-anomalies">
              {groupData.anomalies.map((anomaly, index) => (
                <AnomalyItemPreview
                  key={anomaly.id}
                  anomaly={anomaly}
                  groupContext={groupData}
                />
              ))}
            </div>
          </Panel>
        ))}
      </Collapse>
    </div>
  );
};

// 异常分组逻辑
function groupAnomaliesByType(anomalies: AnomalyData[]) {
  const groups = {
    hardware: {
      name: '硬件异常组',
      principle: '标记异常传感器，安排检修计划，加强人工巡检',
      urgency: 'low',
      urgencyText: '计划处理',
      estimatedTime: '1-7天',
      anomalies: []
    },
    temperature: {
      name: '温度异常组',
      principle: '立即通风降温，监控温度变化，必要时进行品质检测',
      urgency: 'high',
      urgencyText: '紧急处理',
      estimatedTime: '立即-24小时',
      anomalies: []
    },
    spatial: {
      name: '空间异常组',
      principle: '区域检查，局部处理，防止异常扩散',
      urgency: 'high',
      urgencyText: '紧急处理',
      estimatedTime: '立即-48小时',
      anomalies: []
    },
    distribution: {
      name: '分布异常组',
      principle: '优化通风策略，均衡温度分布，改善储存环境',
      urgency: 'medium',
      urgencyText: '密切关注',
      estimatedTime: '1-72小时',
      anomalies: []
    }
  };

  // 将异常分配到对应分组
  anomalies.forEach(anomaly => {
    const groupType = determineAnomalyGroup(anomaly.type);
    if (groups[groupType]) {
      groups[groupType].anomalies.push(anomaly);
    }
  });

  // 只返回有异常的分组
  return Object.fromEntries(
    Object.entries(groups).filter(([_, group]) => group.anomalies.length > 0)
  );
}
```

#### 2.2.5 第三层：单项异常详情设计

```tsx
// AnomalyDetailLayer.tsx - 第三层：单项异常详情
import React, { useState } from 'react';
import { Card, Collapse, Table, Button, Tag, Progress, Descriptions } from 'antd';
import { ExperimentOutlined, ToolOutlined, BarChartOutlined } from '@ant-design/icons';

interface AnomalyDetailLayerProps {
  anomaly: AnomalyData;
  groupContext: GroupData;
}

const AnomalyDetailLayer: React.FC<AnomalyDetailLayerProps> = ({ anomaly, groupContext }) => {
  const [executionStatus, setExecutionStatus] = useState<'pending' | 'executing' | 'completed'>('pending');

  return (
    <Card className="anomaly-detail-layer" size="small">
      <div className="anomaly-header">
        <div className="anomaly-title">
          <Tag color={getSeverityColor(anomaly.severity)}>{anomaly.type}</Tag>
          <span className="confidence-badge">AI置信度: {(anomaly.confidence * 100).toFixed(1)}%</span>
        </div>
      </div>

      <Collapse defaultActiveKey={['description']} ghost>
        {/* 异常描述 */}
        <Collapse.Panel header="🔍 异常描述" key="description">
          <div className="anomaly-description">
            <p><strong>具体表现：</strong>{anomaly.description}</p>
            <p><strong>影响范围：</strong>{anomaly.affectedArea}</p>
            <p><strong>发现时间：</strong>{anomaly.detectedTime}</p>
            {anomaly.riskLevel && (
              <p><strong>风险等级：</strong>
                <Tag color={getRiskColor(anomaly.riskLevel)}>{anomaly.riskLevel}</Tag>
              </p>
            )}
          </div>
        </Collapse.Panel>

        {/* 判定依据 */}
        <Collapse.Panel header="📊 判定依据" key="evidence">
          <div className="evidence-section">
            <h4>关键特征数据</h4>
            <Table
              size="small"
              dataSource={anomaly.evidenceData.keyFeatures}
              columns={[
                {
                  title: '特征指标',
                  dataIndex: 'name',
                  key: 'name',
                },
                {
                  title: '当前值',
                  dataIndex: 'currentValue',
                  key: 'currentValue',
                  render: (value, record) => (
                    <span className={record.isExceeded ? 'exceeded-value' : 'normal-value'}>
                      {value} {record.unit}
                      {record.isExceeded && ' ⚠️'}
                    </span>
                  ),
                },
                {
                  title: '安全阈值',
                  dataIndex: 'threshold',
                  key: 'threshold',
                  render: (value, record) => `${record.operator} ${value} ${record.unit}`,
                },
                {
                  title: '状态',
                  key: 'status',
                  render: (_, record) => (
                    <Tag color={record.isExceeded ? 'red' : 'green'}>
                      {record.isExceeded ? '超标' : '正常'}
                    </Tag>
                  ),
                },
              ]}
              pagination={false}
            />

            <div className="ai-decision-path" style={{ marginTop: 16 }}>
              <h4>AI决策路径</h4>
              <div className="decision-steps">
                {anomaly.evidenceData.decisionPath.map((step, index) => (
                  <div key={index} className="decision-step">
                    <div className="step-indicator">{index + 1}</div>
                    <div className="step-content">
                      <div className="step-title">{step.title}</div>
                      <div className="step-description">{step.description}</div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </Collapse.Panel>

        {/* 作业建议 */}
        <Collapse.Panel header="🛠️ 作业建议" key="actions">
          <div className="action-plan-section">
            {/* 立即行动 */}
            <Card title="⚡ 立即行动" size="small" className="immediate-actions">
              {anomaly.actionPlan.immediateActions.map((action, index) => (
                <div key={index} className="action-item">
                  <div className="action-step">
                    <span className="step-number">{index + 1}</span>
                    <span className="action-text">{action.description}</span>
                  </div>
                  <div className="action-meta">
                    <Tag color="blue">预计时间: {action.estimatedTime}</Tag>
                    {action.requiredPersonnel && (
                      <Tag color="green">需要人员: {action.requiredPersonnel}</Tag>
                    )}
                  </div>
                </div>
              ))}

              <div className="action-controls">
                <Button
                  type="primary"
                  icon={<ToolOutlined />}
                  onClick={() => executeActions('immediate')}
                  disabled={executionStatus === 'executing'}
                >
                  {executionStatus === 'pending' ? '执行建议' :
                   executionStatus === 'executing' ? '执行中...' : '已执行'}
                </Button>
                <Button icon={<BarChartOutlined />}>查看支撑数据</Button>
              </div>
            </Card>

            {/* 预期效果 */}
            <Card title="🎯 预期效果" size="small" style={{ marginTop: 12 }}>
              <Descriptions column={1} size="small">
                <Descriptions.Item label="预期改善">
                  {anomaly.expectedOutcome.improvement}
                </Descriptions.Item>
                <Descriptions.Item label="预计时间">
                  {anomaly.expectedOutcome.timeframe}
                </Descriptions.Item>
                <Descriptions.Item label="成功指标">
                  {anomaly.expectedOutcome.successCriteria}
                </Descriptions.Item>
              </Descriptions>
            </Card>
          </div>
        </Collapse.Panel>

        {/* 支撑数据 */}
        <Collapse.Panel header="📈 支撑数据" key="data">
          <div className="supporting-data-section">
            <div className="data-tabs">
              <Button.Group>
                <Button icon={<BarChartOutlined />}>温度阵列</Button>
                <Button icon={<ExperimentOutlined />}>趋势图表</Button>
                <Button>历史对比</Button>
              </Button.Group>
            </div>

            <div className="data-visualization" style={{ marginTop: 16 }}>
              <TemperatureArrayVisualization anomaly={anomaly} />
            </div>
          </div>
        </Collapse.Panel>
      </Collapse>
    </Card>
  );
};

// 参数化模板设计
interface ParameterizedTemplate {
  baseType: 'local_heating' | 'overall_heating' | 'sensor_fault' | 'temp_rise';
  region?: 'surface' | 'middle' | 'bottom' | 'vertical';
  severity: 'low' | 'medium' | 'high' | 'critical';
  parameters: Record<string, any>;
}

const generateParameterizedContent = (template: ParameterizedTemplate) => {
  const regionNames = {
    surface: '表层',
    middle: '中部',
    bottom: '底部',
    vertical: '垂直'
  };

  const descriptions = {
    local_heating: `检测到${regionNames[template.region] || ''}区域温度异常升高，存在明显的局部发热现象`,
    overall_heating: `检测到${regionNames[template.region] || ''}区域整体温度持续上升，可能影响粮食储存安全`,
    sensor_fault: `传感器${template.parameters.sensorId}数据异常，可能存在硬件故障`,
    temp_rise: `温度上升速率异常，${template.parameters.timeframe}内上升${template.parameters.riseAmount}°C`
  };

  const actionTemplates = {
    local_heating: [
      { description: `检查${regionNames[template.region]}区域密封性`, estimatedTime: '30分钟' },
      { description: '进行感官检查', estimatedTime: '15分钟' },
      { description: '启动局部通风', estimatedTime: '5分钟' }
    ],
    sensor_fault: [
      { description: `标记传感器${template.parameters.sensorId}为异常`, estimatedTime: '5分钟' },
      { description: '安排检修计划', estimatedTime: '1天' },
      { description: '加强该区域人工巡检', estimatedTime: '持续' }
    ]
  };

  return {
    description: descriptions[template.baseType],
    actions: actionTemplates[template.baseType] || []
  };
};

interface DiagnosisDetail {
  alertType: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
  description: string;
  suggestion: string;
  confidence: number;
  affectedArea: string;
  keyFeatures: { name: string; value: number; threshold: number; }[];
  evidenceData: any[];
}

const SiloDetailPanel: React.FC<{ siloId: string }> = ({ siloId }) => {
  const [activeTab, setActiveTab] = useState('diagnosis');
  const diagnosisData = getDiagnosisData(siloId);

  return (
    <Card
      title={`${siloId} 详细诊断报告`}
      className="silo-detail-panel"
      style={{ marginTop: 20 }}
    >
      <Tabs activeKey={activeTab} onChange={setActiveTab}>
        {/* 核心诊断标签页 */}
        <Tabs.TabPane tab="🔍 AI诊断结果" key="diagnosis">
          <DiagnosisResultsTab data={diagnosisData} />
        </Tabs.TabPane>

        {/* 作业建议标签页 */}
        <Tabs.TabPane tab="📋 作业建议" key="suggestions">
          <OperationSuggestionsTab data={diagnosisData} />
        </Tabs.TabPane>

        {/* 判定依据标签页 */}
        <Tabs.TabPane tab="📊 判定依据" key="evidence">
          <DiagnosisEvidenceTab data={diagnosisData} />
        </Tabs.TabPane>

        {/* 粮温阵列标签页 */}
        <Tabs.TabPane tab="🌡️ 粮温阵列" key="temperature">
          <TemperatureArrayTab siloId={siloId} />
        </Tabs.TabPane>

        {/* 历史记录标签页 */}
        <Tabs.TabPane tab="📈 历史记录" key="history">
          <HistoryRecordsTab siloId={siloId} />
        </Tabs.TabPane>
      </Tabs>
    </Card>
  );
};

// AI诊断结果展示
const DiagnosisResultsTab: React.FC<{ data: DiagnosisDetail[] }> = ({ data }) => {
  return (
    <div className="diagnosis-results">
      {data.map((diagnosis, index) => (
        <Card
          key={index}
          className="diagnosis-item"
          style={{ marginBottom: 16 }}
          title={
            <div className="diagnosis-header">
              <Tag color={getSeverityColor(diagnosis.severity)}>
                {diagnosis.alertType}
              </Tag>
              <span className="confidence">
                AI置信度: {(diagnosis.confidence * 100).toFixed(1)}%
              </span>
            </div>
          }
        >
          <Row gutter={16}>
            <Col span={12}>
              <Descriptions column={1} size="small">
                <Descriptions.Item label="异常描述">
                  {diagnosis.description}
                </Descriptions.Item>
                <Descriptions.Item label="影响区域">
                  <Tag color="blue">{diagnosis.affectedArea}</Tag>
                </Descriptions.Item>
                <Descriptions.Item label="严重程度">
                  <Tag color={getSeverityColor(diagnosis.severity)}>
                    {diagnosis.severity.toUpperCase()}
                  </Tag>
                </Descriptions.Item>
              </Descriptions>
            </Col>
            <Col span={12}>
              <div className="key-features">
                <h4>关键特征指标</h4>
                {diagnosis.keyFeatures.map((feature, idx) => (
                  <div key={idx} className="feature-item">
                    <span className="feature-name">{feature.name}:</span>
                    <span className={`feature-value ${feature.value > feature.threshold ? 'exceeded' : 'normal'}`}>
                      {feature.value}
                      {feature.value > feature.threshold && ' ⚠️ 超阈值'}
                    </span>
                  </div>
                ))}
              </div>
            </Col>
          </Row>
        </Card>
      ))}
    </div>
  );
};

// 作业建议展示 - 保管员最关心的操作指导
const OperationSuggestionsTab: React.FC<{ data: DiagnosisDetail[] }> = ({ data }) => {
  const [executedSuggestions, setExecutedSuggestions] = useState<Set<string>>(new Set());

  const handleExecuteSuggestion = (suggestionId: string) => {
    setExecutedSuggestions(prev => new Set([...prev, suggestionId]));
    // 记录操作执行
    recordOperationExecution(suggestionId);
  };

  return (
    <div className="operation-suggestions">
      {data.map((diagnosis, index) => (
        <Card
          key={index}
          className="suggestion-card"
          style={{ marginBottom: 16 }}
          title={
            <div className="suggestion-header">
              <BulbOutlined style={{ color: '#faad14' }} />
              <span style={{ marginLeft: 8 }}>{diagnosis.alertType} - 处理建议</span>
            </div>
          }
        >
          <div className="suggestion-content">
            <div className="suggestion-text">
              {diagnosis.suggestion}
            </div>

            <div className="suggestion-actions">
              <Button
                type="primary"
                onClick={() => handleExecuteSuggestion(`${index}`)}
                disabled={executedSuggestions.has(`${index}`)}
              >
                {executedSuggestions.has(`${index}`) ? '✅ 已执行' : '执行建议'}
              </Button>
              <Button type="default" style={{ marginLeft: 8 }}>
                查看详细步骤
              </Button>
              <Button type="default" style={{ marginLeft: 8 }}>
                记录执行结果
              </Button>
            </div>

            {executedSuggestions.has(`${index}`) && (
              <div className="execution-feedback">
                <Timeline size="small">
                  <Timeline.Item color="green">
                    建议已执行 - {new Date().toLocaleString()}
                  </Timeline.Item>
                  <Timeline.Item color="blue">
                    等待效果观察...
                  </Timeline.Item>
                </Timeline>
              </div>
            )}
          </div>
        </Card>
      ))}
    </div>
  );
};
// 判定依据展示 - 让保管员理解AI的判断逻辑
const DiagnosisEvidenceTab: React.FC<{ data: DiagnosisDetail[] }> = ({ data }) => {
  return (
    <div className="diagnosis-evidence">
      {data.map((diagnosis, index) => (
        <Card
          key={index}
          title={`${diagnosis.alertType} - 判定依据分析`}
          style={{ marginBottom: 16 }}
        >
          <Row gutter={16}>
            <Col span={12}>
              <h4>特征数据分析</h4>
              <Table
                size="small"
                dataSource={diagnosis.keyFeatures}
                columns={[
                  {
                    title: '特征名称',
                    dataIndex: 'name',
                    key: 'name',
                  },
                  {
                    title: '当前值',
                    dataIndex: 'value',
                    key: 'value',
                    render: (value, record) => (
                      <span className={value > record.threshold ? 'text-danger' : 'text-normal'}>
                        {value}
                      </span>
                    ),
                  },
                  {
                    title: '正常阈值',
                    dataIndex: 'threshold',
                    key: 'threshold',
                  },
                  {
                    title: '状态',
                    key: 'status',
                    render: (_, record) => (
                      <Tag color={record.value > record.threshold ? 'red' : 'green'}>
                        {record.value > record.threshold ? '异常' : '正常'}
                      </Tag>
                    ),
                  },
                ]}
                pagination={false}
              />
            </Col>
            <Col span={12}>
              <h4>AI决策路径</h4>
              <div className="decision-path">
                <Timeline>
                  <Timeline.Item color="blue">
                    数据采集: 获取传感器数据
                  </Timeline.Item>
                  <Timeline.Item color="orange">
                    特征计算: 计算多维度特征
                  </Timeline.Item>
                  <Timeline.Item color="red">
                    异常检测: 发现 {diagnosis.alertType}
                  </Timeline.Item>
                  <Timeline.Item color="green">
                    置信度评估: {(diagnosis.confidence * 100).toFixed(1)}%
                  </Timeline.Item>
                </Timeline>
              </div>
            </Col>
          </Row>
        </Card>
      ))}
    </div>
  );
};

// 粮温阵列展示 - 保管员验证AI判断的重要工具
const TemperatureArrayTab: React.FC<{ siloId: string }> = ({ siloId }) => {
  const [viewMode, setViewMode] = useState<'heatmap' | 'table' | '3d'>('heatmap');
  const temperatureData = getTemperatureArrayData(siloId);

  return (
    <div className="temperature-array">
      <div className="view-controls">
        <Button.Group>
          <Button
            type={viewMode === 'heatmap' ? 'primary' : 'default'}
            onClick={() => setViewMode('heatmap')}
          >
            🔥 热力图
          </Button>
          <Button
            type={viewMode === 'table' ? 'primary' : 'default'}
            onClick={() => setViewMode('table')}
          >
            📊 数据表
          </Button>
          <Button
            type={viewMode === '3d' ? 'primary' : 'default'}
            onClick={() => setViewMode('3d')}
          >
            🎯 3D视图
          </Button>
        </Button.Group>
      </div>

      {viewMode === 'heatmap' && (
        <TemperatureHeatmapView data={temperatureData} />
      )}

      {viewMode === 'table' && (
        <TemperatureTableView data={temperatureData} />
      )}

      {viewMode === '3d' && (
        <Temperature3DView data={temperatureData} />
      )}

      {/* 异常点标注 */}
      <div className="anomaly-markers">
        <h4>异常点位标注</h4>
        <div className="markers-list">
          {temperatureData.anomalies?.map((anomaly, index) => (
            <Tag
              key={index}
              color="red"
              style={{ margin: 4 }}
            >
              {anomaly.position} - {anomaly.temperature}°C
            </Tag>
          ))}
        </div>
      </div>
    </div>
  );
};

// 温度热力图组件
const TemperatureHeatmapView: React.FC<{ data: any }> = ({ data }) => {
  const chartRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (!chartRef.current) return;

    const chart = echarts.init(chartRef.current);
    const option = {
      title: {
        text: '粮温分布热力图',
        left: 'center'
      },
      tooltip: {
        trigger: 'item',
        formatter: (params: any) => {
          return `位置: (${params.data[0]}, ${params.data[1]})<br/>
                  温度: ${params.data[2]}°C<br/>
                  传感器: ${params.data[3] || 'N/A'}`;
        }
      },
      visualMap: {
        min: 10,
        max: 35,
        calculable: true,
        orient: 'horizontal',
        left: 'center',
        bottom: '5%',
        inRange: {
          color: [
            '#313695', '#4575b4', '#74add1', '#abd9e9',
            '#e0f3f8', '#ffffcc', '#fee090', '#fdae61',
            '#f46d43', '#d73027', '#a50026'
          ]
        }
      },
      xAxis: {
        type: 'value',
        name: 'X坐标 (m)',
        splitLine: { show: true }
      },
      yAxis: {
        type: 'value',
        name: 'Y坐标 (m)',
        splitLine: { show: true }
      },
      series: [{
        name: '温度',
        type: 'heatmap',
        data: data.heatmapData,
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowColor: 'rgba(0, 0, 0, 0.5)'
          }
        }
      }]
    };

    chart.setOption(option);

    return () => chart.dispose();
  }, [data]);

  return <div ref={chartRef} style={{ width: '100%', height: '500px' }} />;
};

```tsx
// SiloOverviewMap.tsx
import React, { useEffect, useRef } from 'react';
import * as THREE from 'three';
import { Card } from 'antd';

interface SiloData {
  id: string;
  name: string;
  position: [number, number, number];
  temperature: number;
  status: 'normal' | 'warning' | 'danger';
}

const SiloOverviewMap: React.FC = () => {
  const mountRef = useRef<HTMLDivElement>(null);
  const sceneRef = useRef<THREE.Scene>();
  const rendererRef = useRef<THREE.WebGLRenderer>();

  useEffect(() => {
    if (!mountRef.current) return;

    // 初始化Three.js场景
    const scene = new THREE.Scene();
    const camera = new THREE.PerspectiveCamera(
      75,
      mountRef.current.clientWidth / mountRef.current.clientHeight,
      0.1,
      1000
    );
    const renderer = new THREE.WebGLRenderer({ antialias: true });
    
    renderer.setSize(
      mountRef.current.clientWidth,
      mountRef.current.clientHeight
    );
    mountRef.current.appendChild(renderer.domElement);

    // 创建仓房3D模型
    const createSilo = (data: SiloData) => {
      const geometry = new THREE.CylinderGeometry(2, 2, 5, 8);
      const material = new THREE.MeshBasicMaterial({
        color: getStatusColor(data.status)
      });
      const silo = new THREE.Mesh(geometry, material);
      silo.position.set(...data.position);
      silo.userData = data;
      return silo;
    };

    // 渲染循环
    const animate = () => {
      requestAnimationFrame(animate);
      renderer.render(scene, camera);
    };

    sceneRef.current = scene;
    rendererRef.current = renderer;
    animate();

    return () => {
      if (mountRef.current && renderer.domElement) {
        mountRef.current.removeChild(renderer.domElement);
      }
    };
  }, []);

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'normal': return 0x52c41a;
      case 'warning': return 0xfaad14;
      case 'danger': return 0xff4d4f;
      default: return 0x1890ff;
    }
  };

  return (
    <Card title="仓房分布概览" className="silo-overview-map">
      <div ref={mountRef} style={{ width: '100%', height: '400px' }} />
    </Card>
  );
};
```

### 2.2 实时监控页面 (Monitoring)

#### 2.2.1 温度热力图组件
```tsx
// TemperatureHeatmap.tsx
import React, { useEffect, useRef } from 'react';
import * as echarts from 'echarts';
import { Card, Select, DatePicker } from 'antd';

interface TemperatureData {
  x: number;
  y: number;
  z: number;
  temperature: number;
}

const TemperatureHeatmap: React.FC = () => {
  const chartRef = useRef<HTMLDivElement>(null);
  const chartInstance = useRef<echarts.ECharts>();

  useEffect(() => {
    if (!chartRef.current) return;

    const chart = echarts.init(chartRef.current);
    chartInstance.current = chart;

    const option = {
      title: {
        text: '仓房温度分布热力图'
      },
      tooltip: {
        trigger: 'item',
        formatter: (params: any) => {
          return `位置: (${params.data[0]}, ${params.data[1]})<br/>
                  温度: ${params.data[2]}°C`;
        }
      },
      visualMap: {
        min: 10,
        max: 35,
        calculable: true,
        orient: 'horizontal',
        left: 'center',
        bottom: '10%',
        inRange: {
          color: ['#313695', '#4575b4', '#74add1', '#abd9e9', 
                  '#e0f3f8', '#ffffcc', '#fee090', '#fdae61', 
                  '#f46d43', '#d73027', '#a50026']
        }
      },
      xAxis: {
        type: 'value',
        name: 'X坐标 (m)'
      },
      yAxis: {
        type: 'value',
        name: 'Y坐标 (m)'
      },
      series: [{
        name: '温度',
        type: 'heatmap',
        data: generateHeatmapData(),
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowColor: 'rgba(0, 0, 0, 0.5)'
          }
        }
      }]
    };

    chart.setOption(option);

    // 响应式处理
    const handleResize = () => chart.resize();
    window.addEventListener('resize', handleResize);

    return () => {
      window.removeEventListener('resize', handleResize);
      chart.dispose();
    };
  }, []);

  const generateHeatmapData = () => {
    // 模拟温度数据生成
    const data = [];
    for (let x = 0; x < 20; x++) {
      for (let y = 0; y < 20; y++) {
        const temp = 15 + Math.random() * 20;
        data.push([x, y, Math.round(temp * 10) / 10]);
      }
    }
    return data;
  };

  return (
    <Card 
      title="温度分布热力图"
      extra={
        <div>
          <Select defaultValue="A仓" style={{ width: 120, marginRight: 8 }}>
            <Select.Option value="A仓">A仓</Select.Option>
            <Select.Option value="B仓">B仓</Select.Option>
            <Select.Option value="C仓">C仓</Select.Option>
          </Select>
          <DatePicker.RangePicker />
        </div>
      }
    >
      <div ref={chartRef} style={{ width: '100%', height: '500px' }} />
    </Card>
  );
};
```

### 2.3 告警管理页面 (Alerts)

#### 2.3.1 告警列表组件
```tsx
// AlertList.tsx
import React, { useState } from 'react';
import { Table, Tag, Button, Modal, Descriptions, Timeline } from 'antd';
import { ExclamationCircleOutlined, CheckCircleOutlined } from '@ant-design/icons';

interface AlertRecord {
  id: string;
  siloId: string;
  siloName: string;
  alertType: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
  description: string;
  suggestion: string;
  timestamp: string;
  status: 'active' | 'acknowledged' | 'resolved';
}

const AlertList: React.FC = () => {
  const [selectedAlert, setSelectedAlert] = useState<AlertRecord | null>(null);
  const [modalVisible, setModalVisible] = useState(false);

  const columns = [
    {
      title: '告警时间',
      dataIndex: 'timestamp',
      key: 'timestamp',
      sorter: true,
    },
    {
      title: '仓房',
      dataIndex: 'siloName',
      key: 'siloName',
    },
    {
      title: '告警类型',
      dataIndex: 'alertType',
      key: 'alertType',
    },
    {
      title: '严重程度',
      dataIndex: 'severity',
      key: 'severity',
      render: (severity: string) => {
        const colorMap = {
          low: 'blue',
          medium: 'orange',
          high: 'red',
          critical: 'purple'
        };
        return <Tag color={colorMap[severity]}>{severity.toUpperCase()}</Tag>;
      },
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status: string) => {
        const statusConfig = {
          active: { color: 'red', text: '活跃' },
          acknowledged: { color: 'orange', text: '已确认' },
          resolved: { color: 'green', text: '已解决' }
        };
        const config = statusConfig[status];
        return <Tag color={config.color}>{config.text}</Tag>;
      },
    },
    {
      title: '操作',
      key: 'action',
      render: (_, record: AlertRecord) => (
        <div>
          <Button 
            type="link" 
            onClick={() => handleViewDetail(record)}
          >
            查看详情
          </Button>
          {record.status === 'active' && (
            <Button 
              type="link" 
              onClick={() => handleAcknowledge(record.id)}
            >
              确认告警
            </Button>
          )}
        </div>
      ),
    },
  ];

  const handleViewDetail = (alert: AlertRecord) => {
    setSelectedAlert(alert);
    setModalVisible(true);
  };

  const handleAcknowledge = (alertId: string) => {
    // 确认告警逻辑
    console.log('Acknowledging alert:', alertId);
  };

  return (
    <div className="alert-list">
      <Table
        columns={columns}
        dataSource={mockAlertData}
        rowKey="id"
        pagination={{
          pageSize: 20,
          showSizeChanger: true,
          showQuickJumper: true,
        }}
      />

      <Modal
        title="告警详情"
        visible={modalVisible}
        onCancel={() => setModalVisible(false)}
        footer={[
          <Button key="close" onClick={() => setModalVisible(false)}>
            关闭
          </Button>,
          selectedAlert?.status === 'active' && (
            <Button 
              key="acknowledge" 
              type="primary"
              onClick={() => handleAcknowledge(selectedAlert.id)}
            >
              确认告警
            </Button>
          ),
        ]}
        width={800}
      >
        {selectedAlert && (
          <div>
            <Descriptions column={2} bordered>
              <Descriptions.Item label="仓房">{selectedAlert.siloName}</Descriptions.Item>
              <Descriptions.Item label="告警类型">{selectedAlert.alertType}</Descriptions.Item>
              <Descriptions.Item label="严重程度">
                <Tag color={getSeverityColor(selectedAlert.severity)}>
                  {selectedAlert.severity.toUpperCase()}
                </Tag>
              </Descriptions.Item>
              <Descriptions.Item label="发生时间">{selectedAlert.timestamp}</Descriptions.Item>
              <Descriptions.Item label="描述" span={2}>
                {selectedAlert.description}
              </Descriptions.Item>
              <Descriptions.Item label="处理建议" span={2}>
                {selectedAlert.suggestion}
              </Descriptions.Item>
            </Descriptions>

            <div style={{ marginTop: 20 }}>
              <h4>处理时间线</h4>
              <Timeline>
                <Timeline.Item 
                  dot={<ExclamationCircleOutlined style={{ color: 'red' }} />}
                  color="red"
                >
                  {selectedAlert.timestamp} - 告警触发
                </Timeline.Item>
                {selectedAlert.status !== 'active' && (
                  <Timeline.Item 
                    dot={<CheckCircleOutlined style={{ color: 'green' }} />}
                    color="green"
                  >
                    已确认处理
                  </Timeline.Item>
                )}
              </Timeline>
            </div>
          </div>
        )}
      </Modal>
    </div>
  );
};

const getSeverityColor = (severity: string) => {
  const colorMap = {
    low: 'blue',
    medium: 'orange',
    high: 'red',
    critical: 'purple'
  };
  return colorMap[severity] || 'default';
};

const mockAlertData: AlertRecord[] = [
  {
    id: '1',
    siloId: 'silo_001',
    siloName: 'A仓',
    alertType: '表层局部发热',
    severity: 'high',
    description: '检测到A仓表层区域温度异常升高，可能存在局部发热现象',
    suggestion: '建议立即检查仓顶密封性，确认有无渗漏，并进行感官检查',
    timestamp: '2024-01-15 14:30:00',
    status: 'active'
  },
  // ... 更多模拟数据
];
```

## 3. 组件设计

### 3.1 实时数据组件
```tsx
// RealTimeData.tsx
import React, { useEffect, useState } from 'react';
import { useWebSocket } from '../hooks/useWebSocket';

interface SensorData {
  sensorId: string;
  temperature: number;
  humidity: number;
  timestamp: string;
}

const RealTimeData: React.FC = () => {
  const [sensorData, setSensorData] = useState<SensorData[]>([]);
  
  const { data, isConnected } = useWebSocket('ws://localhost:8080/ws/sensors');

  useEffect(() => {
    if (data) {
      setSensorData(prevData => {
        const newData = [...prevData];
        const index = newData.findIndex(item => item.sensorId === data.sensorId);
        
        if (index >= 0) {
          newData[index] = data;
        } else {
          newData.push(data);
        }
        
        return newData.slice(-100); // 保持最新100条数据
      });
    }
  }, [data]);

  return (
    <div className="real-time-data">
      <div className="connection-status">
        连接状态: {isConnected ? '已连接' : '断开连接'}
      </div>
      {/* 渲染实时数据 */}
    </div>
  );
};
```

### 3.2 自定义Hooks
```tsx
// hooks/useWebSocket.ts
import { useEffect, useState, useRef } from 'react';

export const useWebSocket = (url: string) => {
  const [data, setData] = useState(null);
  const [isConnected, setIsConnected] = useState(false);
  const ws = useRef<WebSocket | null>(null);

  useEffect(() => {
    ws.current = new WebSocket(url);
    
    ws.current.onopen = () => {
      setIsConnected(true);
    };
    
    ws.current.onmessage = (event) => {
      const receivedData = JSON.parse(event.data);
      setData(receivedData);
    };
    
    ws.current.onclose = () => {
      setIsConnected(false);
    };
    
    ws.current.onerror = (error) => {
      console.error('WebSocket error:', error);
      setIsConnected(false);
    };

    return () => {
      if (ws.current) {
        ws.current.close();
      }
    };
  }, [url]);

  return { data, isConnected };
};
```

## 4. 响应式设计

### 4.1 移动端适配
```scss
// responsive.scss
@media (max-width: 768px) {
  .dashboard {
    .stats-row {
      .ant-col {
        margin-bottom: 16px;
      }
    }
    
    .main-content {
      .ant-col {
        margin-bottom: 16px;
      }
    }
  }
  
  .silo-overview-map {
    .three-container {
      height: 300px !important;
    }
  }
}

@media (max-width: 576px) {
  .alert-list {
    .ant-table {
      font-size: 12px;
    }
  }
}
```

## 5. 性能优化

### 5.1 代码分割
```tsx
// 路由懒加载
import { lazy, Suspense } from 'react';
import { Spin } from 'antd';

const Dashboard = lazy(() => import('../pages/Dashboard'));
const Monitoring = lazy(() => import('../pages/Monitoring'));
const Analysis = lazy(() => import('../pages/Analysis'));

const AppRouter = () => (
  <Suspense fallback={<Spin size="large" />}>
    <Routes>
      <Route path="/dashboard" element={<Dashboard />} />
      <Route path="/monitoring" element={<Monitoring />} />
      <Route path="/analysis" element={<Analysis />} />
    </Routes>
  </Suspense>
);
```

### 5.2 数据缓存策略
```tsx
// 使用RTK Query进行数据缓存
import { createApi, fetchBaseQuery } from '@reduxjs/toolkit/query/react';

export const grainApi = createApi({
  reducerPath: 'grainApi',
  baseQuery: fetchBaseQuery({
    baseUrl: '/api/',
  }),
  tagTypes: ['Silo', 'Alert', 'Sensor'],
  endpoints: (builder) => ({
    getSilos: builder.query({
      query: () => 'silos',
      providesTags: ['Silo'],
      // 缓存5分钟
      keepUnusedDataFor: 300,
    }),
    getAlerts: builder.query({
      query: (params) => ({
        url: 'alerts',
        params,
      }),
      providesTags: ['Alert'],
      // 实时数据，缓存30秒
      keepUnusedDataFor: 30,
    }),
  }),
});
```

## 6. 保管员工作流程优化

### 6.1 核心工作流程设计
```
保管员日常工作流程：
1. 登录系统 → 查看快速状态栏
2. 识别异常仓房 → 点击查看详情
3. 查看AI诊断结果 → 理解异常类型和严重程度
4. 查看作业建议 → 获取具体操作指导
5. 查看判定依据 → 验证AI判断的合理性
6. 查看粮温阵列 → 人工对照确认
7. 执行建议操作 → 记录执行结果
8. 持续监控效果 → 形成经验积累
```

### 6.2 关键设计原则

#### 6.2.1 一目了然原则
- **状态可视化**：用颜色、图标、动画直观表示仓房状态
- **优先级排序**：紧急问题优先显示，正常状态次要展示
- **信息分层**：重要信息大字体显示，详细信息分层展示

#### 6.2.2 操作便捷原则
- **一键直达**：从异常发现到详细诊断一键到达
- **批量操作**：支持多个建议同时执行和记录
- **快捷键支持**：常用操作提供键盘快捷键

#### 6.2.3 可信度透明原则
- **AI置信度显示**：每个诊断结果都显示置信度
- **判定依据展示**：清晰展示AI基于哪些数据做出判断
- **人工验证支持**：提供原始数据供保管员验证

## 7. 总结

通过以保管员工作流程为核心的前端设计，我们构建了一个真正贴合实际业务需求的粮情分析系统。该系统具有以下特点：

### 7.1 核心优势
- **快速识别**：一眼看出异常仓房和问题严重程度
- **详细诊断**：点击即可查看完整的AI诊断信息
- **可信建议**：提供具体可执行的作业建议
- **透明判定**：清晰展示AI判断的依据和逻辑
- **数据验证**：提供粮温阵列供人工对照确认
- **操作追踪**：完整记录操作过程和效果

### 7.2 技术特色
- **响应式设计**：支持PC端和移动端无缝切换
- **实时更新**：数据实时刷新，状态即时反映
- **交互友好**：直观的视觉设计和流畅的操作体验
- **可扩展性**：模块化设计，便于功能扩展

### 7.3 业务价值
- **提高效率**：显著减少保管员的工作量和响应时间
- **降低风险**：及时发现和处理粮情异常，避免损失
- **科学决策**：基于AI分析的科学决策支持
- **经验积累**：系统化记录和积累保管经验

### 2.3 "总-分-据"架构的核心优势

#### 2.3.1 认知负荷优化
- **总述层**：3秒内掌握整体情况，减少信息过载
- **分类层**：按处理方式分组，符合保管员工作习惯
- **详据层**：按需深入，避免不必要的认知负担

#### 2.3.2 决策效率提升
- **优先级明确**：总述层直接给出处理优先级
- **分组处理**：相似异常统一处理，提高效率
- **参数化模板**：减少重复信息，突出关键差异

#### 2.3.3 可信度建立
- **透明判定**：清晰展示AI判断依据
- **数据支撑**：提供完整的支撑数据
- **人工验证**：支持保管员验证和确认

## 8. 层次化架构实施要点

### 8.1 数据组织策略
```typescript
// 数据预处理管道
class AnomalyDataProcessor {
  // 第一步：异常分组
  groupAnomalies(anomalies: AnomalyData[]): GroupedAnomalies {
    return {
      hardware: anomalies.filter(a => this.isHardwareAnomaly(a.type)),
      temperature: anomalies.filter(a => this.isTemperatureAnomaly(a.type)),
      spatial: anomalies.filter(a => this.isSpatialAnomaly(a.type)),
      distribution: anomalies.filter(a => this.isDistributionAnomaly(a.type))
    };
  }

  // 第二步：计算总述数据
  calculateSummary(anomalies: AnomalyData[]): AnomalySummary {
    const criticalCount = anomalies.filter(a => a.severity === 'critical').length;
    const urgencyLevel = this.determineUrgencyLevel(anomalies);
    const prioritizedActions = this.generatePriorityActions(anomalies);

    return {
      totalCount: anomalies.length,
      criticalCount,
      urgencyLevel,
      prioritizedActions,
      resourceRequirements: this.calculateResources(anomalies)
    };
  }

  // 第三步：参数化内容生成
  generateParameterizedContent(anomaly: AnomalyData): ParameterizedContent {
    const template = this.getTemplate(anomaly.type);
    return this.applyParameters(template, anomaly.parameters);
  }
}
```

### 8.2 组件复用策略
```typescript
// 可配置的异常展示组件
const ConfigurableAnomalyDisplay: React.FC<{
  anomaly: AnomalyData;
  displayMode: 'summary' | 'grouped' | 'detailed';
  template?: ParameterizedTemplate;
}> = ({ anomaly, displayMode, template }) => {
  switch (displayMode) {
    case 'summary':
      return <AnomalySummaryItem anomaly={anomaly} />;
    case 'grouped':
      return <GroupedAnomalyItem anomaly={anomaly} />;
    case 'detailed':
      return template ?
        <ParameterizedAnomalyDetail template={template} /> :
        <StandardAnomalyDetail anomaly={anomaly} />;
  }
};
```

### 8.3 用户体验优化
- **渐进式展示**：从总述到详情的平滑过渡
- **智能折叠**：根据重要性自动展开/折叠
- **快速导航**：提供层次间的快速跳转
- **状态记忆**：记住用户的展开/折叠偏好

这套"总-分-据"层次化架构真正实现了以保管员为中心的用户体验，将复杂的AI技术转化为简单易用的工作工具，为粮食安全保障提供了强有力的技术支撑。
