# 重构版粮情分析系统需求分析文档

## 1. 异常分类体系重新分析与定义

### 1.1 现有18种异常类型的分类逻辑问题

#### 1.1.1 分类维度混乱问题
**发现的问题**：
现有的18种异常类型混合了多个分类维度：
- **现象维度**：表层局部发热、中部局部发热、底部局部发热
- **原因维度**：硬件异常-点位、硬件异常-线缆
- **程度维度**：温升速率异常、超最高温
- **空间维度**：同层/圈不均、外圈粮温过高

**核心问题**：设备异常导致的温度异常，既可以归类为"设备异常"，也可以归类为"温度异常"，存在分类边界模糊的问题。

#### 1.1.2 表象与根因混淆问题
**现状分析**：
```
混淆示例：
- "硬件异常-点位" → 这是根本原因，不是异常现象
- "表层局部发热" → 这是异常现象，不是根本原因
- "新粮入仓后熟" → 这既包含现象又包含原因
```

**问题本质**：没有明确区分"异常现象"（What）和"异常原因"（Why）。

### 1.2 重新定义异常分类体系

#### 1.2.1 分类原则
1. **现象导向**：异常检测只识别"现象"，不判断"原因"
2. **可观测性**：所有异常类型都应该是可以通过传感器数据直接观测到的现象
3. **互斥性**：不同异常类型之间应该相互独立，避免重叠
4. **完备性**：覆盖所有可能的粮情异常现象

#### 1.2.2 重新设计的异常分类体系

基于保持现象详细程度和业务区分度的原则，重新设计异常分类体系：

**维度一：空间分布异常（Spatial Distribution Anomalies）**
```
1. 表层局部发热 (Surface Local Heating)
   - 现象：表层局部区域温度显著高于周围区域
   - 业务价值：局部问题，可能是局部水分超标、虫害或密封问题
   - 处置策略：局部检查、局部处理、防止扩散

2. 表层整体发热 (Surface Overall Heating)
   - 现象：表层整体温度高于安全阈值
   - 业务价值：整体问题，可能是环境因素或整体品质问题
   - 处置策略：整体通风、环境控制、全面检查

3. 中部局部发热 (Middle Local Heating)
   - 现象：中部局部区域温度异常升高
   - 业务价值：内部局部问题，检查和处理难度较大
   - 处置策略：深度检查、可能需要扒粮处理

4. 中部整体发热 (Middle Overall Heating)
   - 现象：中部整体温度异常升高
   - 业务价值：内部整体问题，风险较高
   - 处置策略：全面通风、可能需要大范围处理

5. 底部局部发热 (Bottom Local Heating)
   - 现象：底部局部区域温度异常升高
   - 业务价值：底部问题，可能与地面湿度、密封相关
   - 处置策略：检查地面密封、底部通风

6. 底部整体发热 (Bottom Overall Heating)
   - 现象：底部整体温度异常升高
   - 业务价值：底部整体问题，可能需要大规模处理
   - 处置策略：底部通风系统检查、整体处理

7. 垂直局部发热 (Vertical Local Heating)
   - 现象：垂直方向局部温度梯度异常
   - 业务价值：垂直通风问题或局部品质问题
   - 处置策略：垂直通风调整、局部检查

8. 垂直整体发热 (Vertical Overall Heating)
   - 现象：整个垂直剖面温度异常
   - 业务价值：垂直通风系统问题或整体品质问题
   - 处置策略：垂直通风系统全面检查
```

**维度二：时间变化异常（Temporal Change Anomalies）**
```
9. 温升速率异常 (Temperature Rise Rate Anomaly)
   - 现象：温度上升速率超过正常范围
   - 业务价值：动态变化监控，预警作用
   - 处置策略：立即通风、密切监控

10. 超最高温 (Exceeding Maximum Temperature)
    - 现象：温度超过安全阈值上限
    - 业务价值：绝对温度控制，安全红线
    - 处置策略：紧急降温、品质检测

11. 新粮入仓后熟 (New Grain Post-Storage Ripening)
    - 现象：新入仓粮食的自然温升过程异常
    - 业务价值：新粮特有现象，需要特殊处理策略
    - 处置策略：控制入仓条件、适度通风
```

**维度三：分布均匀性异常（Distribution Uniformity Anomalies）**
```
12. 同层/圈不均 (Same Layer/Ring Unevenness)
    - 现象：同一水平层内温度分布不均匀
    - 业务价值：水平通风效果评估
    - 处置策略：调整水平通风策略

13. 外圈粮温过高 (Outer Ring Temperature Too High)
    - 现象：仓房外圈区域温度过高
    - 业务价值：边缘效应监控，可能与密封、隔热相关
    - 处置策略：检查仓房密封、改善隔热

14. 仓内温差过大 (Excessive Temperature Difference)
    - 现象：仓内不同区域温差超过安全范围
    - 业务价值：整体温度均匀性评估
    - 处置策略：优化通风分布、改善温度均匀性

15. 整仓发热 (Whole Silo Heating)
    - 现象：整个仓房温度整体升高
    - 业务价值：整体风险评估，最高级别告警
    - 处置策略：全面应急处理、可能需要出仓
```

**维度四：数据质量异常（Data Quality Anomalies）**
```
16. 硬件异常-点位 (Hardware Anomaly - Point)
    - 现象：单个传感器点位数据异常
    - 业务价值：数据可靠性保证，影响诊断准确性
    - 处置策略：传感器检修、数据校正

17. 硬件异常-线缆 (Hardware Anomaly - Cable)
    - 现象：传感器线缆连接异常导致数据问题
    - 业务价值：系统可靠性保证
    - 处置策略：线缆检修、连接修复
```

#### 1.2.3 异常类型保留和合并的具体理由

**保留的异常类型（15种）及理由**：

1. **空间分布异常保持详细分类**：
   - 局部vs整体：成因不同（局部可能是点源问题，整体可能是系统性问题）
   - 不同层次：处理难度和策略差异巨大（表层易处理，底部难处理）
   - 垂直方向：反映通风系统效果，需要专门的处理策略

2. **时间变化异常细分保留**：
   - 温升速率异常：动态监控，预警性质
   - 超最高温：绝对阈值，安全红线
   - 新粮入仓后熟：特殊现象，需要专门的处理策略

3. **分布均匀性异常详细保留**：
   - 同层不均：水平通风问题
   - 外圈过高：边缘效应，密封隔热问题
   - 温差过大：整体均匀性问题
   - 整仓发热：最高级别风险

4. **硬件异常分类保留**：
   - 点位异常vs线缆异常：故障类型不同，维修策略不同

**唯一合并的异常类型**：
- **新粮入仓后熟**：虽然保留为独立类型，但在现象层面可以用温升速率异常的检测算法，只是在原因分析时会识别为"新粮后熟"这一特殊原因

**从18种调整为17种的原因**：
- 原有的"温升速率异常"和"超最高温"虽然都是时间相关，但判定标准和处理策略完全不同，应该保持独立
- 只有"新粮入仓后熟"可以在检测算法层面与"温升速率异常"共用，但在业务层面仍需要区分

### 1.3 异常现象与根本原因的边界定义

#### 1.3.1 异常现象（What）- 异常检测模块负责
**定义**：通过传感器数据可以直接观测到的温度分布或变化模式的偏离
**特征**：
- 可量化：有明确的数值指标和阈值
- 可观测：基于现有传感器数据可以检测
- 现象性：描述"是什么"而不是"为什么"

#### 1.3.2 根本原因（Why）- 根本原因分析模块负责
**定义**：导致异常现象发生的深层次原因
**分类**：
- 环境因素：外界温湿度、气象条件等
- 粮食品质：水分、虫害、霉变等
- 设备故障：通风、制冷、密封等设备问题
- 操作管理：通风时机、维护、响应等人为因素

#### 1.3.3 边界划分示例
```
异常现象：表层温度异常
├─ 现象描述：表层区域温度高于安全阈值
├─ 检测方法：基于温度传感器数据的统计分析
└─ 可能的根本原因：
   ├─ 环境因素：外界高温、太阳直射
   ├─ 粮食品质：表层水分过高、虫害活动
   ├─ 设备故障：仓顶密封不良、通风不畅
   └─ 操作管理：通风时机不当、检查不及时
```

## 2. 正确的业务流程定义

### 2.1 完整业务流程
```
数据采集 → 数据预处理 → 异常检测 → 根本原因分析 → 综合诊断 → 针对性建议 → 执行反馈 → 效果评估
```

### 2.2 各阶段详细定义

#### 2.2.1 数据采集阶段
**输入**：多源传感器数据
- 温度传感器：粮温分布数据
- 湿度传感器：仓内湿度数据
- 环境传感器：外界温湿度、气压等
- 设备状态：通风、制冷设备运行状态
- 操作记录：人工操作日志

**输出**：标准化的多维时序数据

#### 2.2.2 数据预处理阶段
**功能**：
- 数据清洗：去除异常值和噪声
- 数据插值：处理缺失数据
- 数据同步：统一时间戳和采样频率
- 质量评估：评估数据可靠性

**输出**：高质量的特征工程输入数据

#### 2.2.3 异常检测阶段
**功能**：识别9大类异常现象
**方法**：基于统计分析和机器学习的异常检测
**输出**：
- 异常类型标识
- 异常严重程度
- 异常空间位置
- 异常时间信息
- 检测置信度

#### 2.2.4 根本原因分析阶段
**输入**：异常检测结果 + 多源数据
**功能**：分析导致异常的根本原因
**方法**：因果推理和关联分析
**输出**：
- 根本原因分类（环境/品质/设备/操作）
- 原因置信度评估
- 因果关系强度
- 时间关联分析

#### 2.2.5 综合诊断阶段
**功能**：整合异常现象和根本原因，形成完整诊断
**输出**：
- 综合诊断报告
- 风险等级评估
- 影响范围分析
- 发展趋势预测

#### 2.2.6 针对性建议阶段
**功能**：基于异常类型和根本原因生成建议
**策略**：
- 治标建议：针对异常现象的应急处理
- 治本建议：针对根本原因的根治措施
- 预防建议：避免类似问题再次发生

#### 2.2.7 执行反馈阶段
**功能**：跟踪建议执行情况和效果
**内容**：
- 执行状态记录
- 效果评估
- 经验积累
- 模型优化反馈

## 3. 重构后的系统架构设计

### 3.1 四模块架构设计

#### 3.1.1 模块一：多维度特征工程引擎
**功能扩展**：
- 原有功能：基于粮温数据的特征计算
- 新增功能：多源数据融合和特征提取

**特征类别**：
```
1. 温度特征（原有）
   - 单点特征：当前温度、温升速率、邻域偏差
   - 聚合特征：区域统计、温度分布、异常比例
   - 空间特征：垂直梯度、径向梯度、层间温差

2. 环境特征（新增）
   - 外界温湿度变化率
   - 气象条件指标
   - 季节性特征
   - 仓内外关联特征

3. 设备特征（新增）
   - 设备运行状态特征
   - 设备性能指标
   - 故障历史特征
   - 维护及时性特征

4. 操作特征（新增）
   - 操作时机特征
   - 操作频次特征
   - 响应时间特征
   - 标准符合度特征

5. 时序特征（增强）
   - 趋势特征
   - 周期性特征
   - 突变点特征
   - 滞后关联特征
```

#### 3.1.2 模块二：AI异常检测模型（重构）
**检测目标**：17类异常现象
**模型架构**：
```
多任务学习框架：
├─ 共享特征提取层
├─ 空间异常检测分支（8类）
│  ├─ 表层局部发热
│  ├─ 表层整体发热
│  ├─ 中部局部发热
│  ├─ 中部整体发热
│  ├─ 底部局部发热
│  ├─ 底部整体发热
│  ├─ 垂直局部发热
│  └─ 垂直整体发热
├─ 时间异常检测分支（3类）
│  ├─ 温升速率异常
│  ├─ 超最高温
│  └─ 新粮入仓后熟
├─ 分布异常检测分支（4类）
│  ├─ 同层/圈不均
│  ├─ 外圈粮温过高
│  ├─ 仓内温差过大
│  └─ 整仓发热
└─ 数据质量检测分支（2类）
   ├─ 硬件异常-点位
   └─ 硬件异常-线缆
```

**技术方案**：
- 基础模型：梯度提升树（LightGBM）
- 深度学习：Transformer用于时序建模
- 异常检测：Isolation Forest用于无监督检测
- 集成学习：多模型融合提高准确性

#### 3.1.3 模块三：根本原因推理引擎（新增）
**核心功能**：基于异常检测结果分析根本原因

**算法设计**：
```
1. 因果推理层
   - 贝叶斯网络建模
   - 因果图构建
   - 条件概率推理

2. 时序关联层
   - 滞后效应分析
   - 格兰杰因果检验
   - 时间序列因果发现

3. 证据融合层
   - 多源证据整合
   - 不确定性处理
   - 置信度评估

4. 知识推理层
   - 专家规则引擎
   - 案例推理
   - 模式匹配
```

**针对17种异常类型的算法策略**：

**空间分布异常（8类）**：
```
表层局部发热：
├─ 算法：局部异常检测 + 空间聚类 + 因果推理
├─ 特征：局部温度梯度、热点面积、邻域温差
├─ 重点原因：局部水分超标、虫害活动、密封问题
└─ 处理策略：局部检查、防止扩散

表层整体发热：
├─ 算法：整体统计分析 + 环境关联分析
├─ 特征：整体温度水平、环境相关性、时间模式
├─ 重点原因：环境因素、整体品质问题、通风不当
└─ 处理策略：整体通风、环境控制

中部局部发热：
├─ 算法：深度异常检测 + 传播模式分析
├─ 特征：内部温度分布、传播路径、持续时间
├─ 重点原因：内部品质变化、局部虫害
└─ 处理策略：深度检查、可能需要扒粮

中部整体发热：
├─ 算法：整体趋势分析 + 多因子关联
├─ 特征：整体温升趋势、多层温度关联
├─ 重点原因：整体品质问题、通风系统故障
└─ 处理策略：全面通风、大范围处理

底部局部发热：
├─ 算法：底部特征分析 + 地面因素关联
├─ 特征：底部温度分布、地面湿度、密封状态
├─ 重点原因：地面湿度、底部密封问题
└─ 处理策略：底部密封检查、底部通风

底部整体发热：
├─ 算法：底部系统分析 + 结构因素评估
├─ 特征：底部整体温度、结构完整性
├─ 重点原因：底部通风系统问题、结构问题
└─ 处理策略：底部系统全面检查

垂直局部发热：
├─ 算法：垂直梯度分析 + 通风效果评估
├─ 特征：垂直温度梯度、通风路径、局部阻塞
├─ 重点原因：垂直通风局部问题、局部阻塞
└─ 处理策略：垂直通风调整、局部疏通

垂直整体发热：
├─ 算法：垂直系统分析 + 整体通风评估
├─ 特征：整体垂直温度分布、通风系统状态
├─ 重点原因：垂直通风系统故障、整体品质问题
└─ 处理策略：垂直通风系统全面检查
```

**时间变化异常（3类）**：
```
温升速率异常：
├─ 算法：时序分析 + 变化率检测 + 滞后关联
├─ 特征：温升速率、加速度、时间模式
├─ 重点原因：品质变化、环境突变、操作不当
└─ 处理策略：立即通风、密切监控

超最高温：
├─ 算法：阈值检测 + 紧急评估 + 风险分析
├─ 特征：绝对温度值、超标程度、影响范围
├─ 重点原因：严重品质问题、设备故障、环境极端
└─ 处理策略：紧急降温、品质检测、风险评估

新粮入仓后熟：
├─ 算法：新粮模式识别 + 生理过程建模
├─ 特征：入仓时间、温升模式、水分状态
├─ 重点原因：新粮生理活动、入仓条件、水分控制
└─ 处理策略：控制入仓条件、适度通风、耐心等待
```

**分布均匀性异常（4类）**：
```
同层/圈不均：
├─ 算法：水平分布分析 + 通风效果评估
├─ 特征：同层温度方差、径向分布、通风均匀性
├─ 重点原因：水平通风不均、仓房结构问题
└─ 处理策略：调整水平通风策略、改善分布

外圈粮温过高：
├─ 算法：边缘效应分析 + 隔热性能评估
├─ 特征：边缘温度、内外温差、隔热效果
├─ 重点原因：隔热不良、密封问题、外界影响
└─ 处理策略：改善隔热、检查密封、边缘通风

仓内温差过大：
├─ 算法：全局分布分析 + 均匀性评估
├─ 特征：全局温度分布、温差统计、均匀性指标
├─ 重点原因：通风分布不均、仓房设计问题
└─ 处理策略：优化通风分布、改善均匀性

整仓发热：
├─ 算法：整体风险评估 + 紧急响应分析
├─ 特征：整体温度水平、发热强度、发展趋势
├─ 重点原因：整体品质严重问题、系统性故障
└─ 处理策略：全面应急处理、考虑出仓
```

**数据质量异常（2类）**：
```
硬件异常-点位：
├─ 算法：单点异常检测 + 传感器诊断
├─ 特征：单点数据质量、邻点对比、历史稳定性
├─ 重点原因：传感器老化、环境腐蚀、物理损坏
└─ 处理策略：传感器检修、数据校正、替换设备

硬件异常-线缆：
├─ 算法：连接诊断 + 信号质量分析
├─ 特征：信号强度、连接稳定性、传输质量
├─ 重点原因：线缆老化、连接松动、环境影响
└─ 处理策略：线缆检修、连接加固、环境改善
```

#### 3.1.4 模块四：智能建议生成器（重构）
**生成策略**：
```
双重建议机制：
├─ 治标建议：基于异常现象的应急处理
│  ├─ 表层温度异常 → 表层通风、温度监控
│  ├─ 温升速率异常 → 立即通风、降温措施
│  └─ 分布异常 → 调整通风策略
└─ 治本建议：基于根本原因的根治措施
   ├─ 环境因素 → 环境控制、隔热改善
   ├─ 品质问题 → 品质检测、处理措施
   ├─ 设备故障 → 设备维修、更换计划
   └─ 操作问题 → 流程优化、培训计划
```

### 3.2 数据流设计
```
多源数据输入 → 特征工程引擎 → 异常检测模型 → 根本原因推理 → 建议生成
     ↓              ↓              ↓              ↓           ↓
   数据质量评估   特征重要性分析   异常置信度评估   原因置信度评估  建议优先级排序
     ↓              ↓              ↓              ↓           ↓
   质量报告       特征解释        异常报告        原因分析报告   执行建议
```

### 3.3 模块间接口设计
```python
# 异常检测模块输出接口
class AnomalyDetectionResult:
    anomaly_type: str           # 异常类型（9种之一）
    severity_level: float       # 严重程度 [0-1]
    confidence: float           # 检测置信度 [0-1]
    spatial_location: dict      # 空间位置信息
    temporal_info: dict         # 时间信息
    affected_area: str          # 影响范围描述
    key_features: dict          # 关键特征值

# 根本原因分析模块输出接口
class RootCauseAnalysisResult:
    primary_cause: str          # 主要原因类型
    cause_confidence: float     # 原因置信度 [0-1]
    contributing_factors: list  # 贡献因素列表
    causal_strength: float      # 因果关系强度
    temporal_correlation: dict  # 时间关联信息
    evidence_summary: dict      # 证据汇总

# 建议生成模块输出接口
class ActionRecommendation:
    immediate_actions: list     # 立即行动建议
    root_cause_actions: list    # 根本原因处理建议
    preventive_measures: list   # 预防措施建议
    priority_order: list        # 优先级排序
    expected_outcome: str       # 预期效果
    resource_requirements: dict # 资源需求
```
