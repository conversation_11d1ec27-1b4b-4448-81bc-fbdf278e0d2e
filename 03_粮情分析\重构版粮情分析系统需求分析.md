# 重构版粮情分析系统需求分析文档

## 1. 异常分类体系重新分析与定义

### 1.1 现有18种异常类型的分类逻辑问题

#### 1.1.1 分类维度混乱问题
**发现的问题**：
现有的18种异常类型混合了多个分类维度：
- **现象维度**：表层局部发热、中部局部发热、底部局部发热
- **原因维度**：硬件异常-点位、硬件异常-线缆
- **程度维度**：温升速率异常、超最高温
- **空间维度**：同层/圈不均、外圈粮温过高

**核心问题**：设备异常导致的温度异常，既可以归类为"设备异常"，也可以归类为"温度异常"，存在分类边界模糊的问题。

#### 1.1.2 表象与根因混淆问题
**现状分析**：
```
混淆示例：
- "硬件异常-点位" → 这是根本原因，不是异常现象
- "表层局部发热" → 这是异常现象，不是根本原因
- "新粮入仓后熟" → 这既包含现象又包含原因
```

**问题本质**：没有明确区分"异常现象"（What）和"异常原因"（Why）。

### 1.2 重新定义异常分类体系

#### 1.2.1 分类原则
1. **现象导向**：异常检测只识别"现象"，不判断"原因"
2. **可观测性**：所有异常类型都应该是可以通过传感器数据直接观测到的现象
3. **互斥性**：不同异常类型之间应该相互独立，避免重叠
4. **完备性**：覆盖所有可能的粮情异常现象

#### 1.2.2 新的异常分类体系

**维度一：空间分布异常（Spatial Distribution Anomalies）**
```
1. 表层温度异常 (Surface Temperature Anomaly)
   - 表层局部高温：局部区域温度显著高于周围
   - 表层整体高温：表层整体温度高于安全阈值

2. 中部温度异常 (Middle Temperature Anomaly)
   - 中部局部高温：中部局部区域温度异常
   - 中部整体高温：中部整体温度异常

3. 底部温度异常 (Bottom Temperature Anomaly)
   - 底部局部高温：底部局部区域温度异常
   - 底部整体高温：底部整体温度异常

4. 垂直温度异常 (Vertical Temperature Anomaly)
   - 垂直温度梯度异常：上下层温差过大
   - 垂直温度倒置：下层温度高于上层
```

**维度二：时间变化异常（Temporal Change Anomalies）**
```
5. 温升速率异常 (Temperature Rise Rate Anomaly)
   - 急速温升：温度上升速率超过正常范围
   - 持续温升：温度持续上升超过安全时长

6. 温度波动异常 (Temperature Fluctuation Anomaly)
   - 温度振荡：温度出现异常的周期性波动
   - 温度突变：温度出现突然的跳跃变化
```

**维度三：分布均匀性异常（Distribution Uniformity Anomalies）**
```
7. 水平分布异常 (Horizontal Distribution Anomaly)
   - 同层温差过大：同一水平层内温度分布不均
   - 径向温差过大：中心与边缘温度差异过大

8. 整体温度异常 (Overall Temperature Anomaly)
   - 整仓高温：整个仓房温度超过安全阈值
   - 整仓低温：整个仓房温度低于正常范围
```

**维度四：数据质量异常（Data Quality Anomalies）**
```
9. 传感器数据异常 (Sensor Data Anomaly)
   - 数据缺失：传感器数据长时间缺失
   - 数据漂移：传感器读数出现系统性偏差
   - 数据噪声：传感器数据出现异常噪声
```

#### 1.2.3 异常类型合并和简化

**从18种简化为9大类异常现象**：
```
原有18种 → 新的9种
├─ 表层局部发热、表层整体发热 → 表层温度异常
├─ 中部局部发热、中部整体发热 → 中部温度异常  
├─ 底部局部发热、底部整体发热 → 底部温度异常
├─ 垂直局部发热、垂直整体发热 → 垂直温度异常
├─ 温升速率异常、超最高温 → 温升速率异常
├─ 同层/圈不均、外圈粮温过高、仓内温差过大 → 分布均匀性异常
├─ 整仓发热 → 整体温度异常
├─ 新粮入仓后熟 → 归入温升速率异常（现象层面）
└─ 硬件异常-点位、硬件异常-线缆 → 传感器数据异常
```

### 1.3 异常现象与根本原因的边界定义

#### 1.3.1 异常现象（What）- 异常检测模块负责
**定义**：通过传感器数据可以直接观测到的温度分布或变化模式的偏离
**特征**：
- 可量化：有明确的数值指标和阈值
- 可观测：基于现有传感器数据可以检测
- 现象性：描述"是什么"而不是"为什么"

#### 1.3.2 根本原因（Why）- 根本原因分析模块负责
**定义**：导致异常现象发生的深层次原因
**分类**：
- 环境因素：外界温湿度、气象条件等
- 粮食品质：水分、虫害、霉变等
- 设备故障：通风、制冷、密封等设备问题
- 操作管理：通风时机、维护、响应等人为因素

#### 1.3.3 边界划分示例
```
异常现象：表层温度异常
├─ 现象描述：表层区域温度高于安全阈值
├─ 检测方法：基于温度传感器数据的统计分析
└─ 可能的根本原因：
   ├─ 环境因素：外界高温、太阳直射
   ├─ 粮食品质：表层水分过高、虫害活动
   ├─ 设备故障：仓顶密封不良、通风不畅
   └─ 操作管理：通风时机不当、检查不及时
```

## 2. 正确的业务流程定义

### 2.1 完整业务流程
```
数据采集 → 数据预处理 → 异常检测 → 根本原因分析 → 综合诊断 → 针对性建议 → 执行反馈 → 效果评估
```

### 2.2 各阶段详细定义

#### 2.2.1 数据采集阶段
**输入**：多源传感器数据
- 温度传感器：粮温分布数据
- 湿度传感器：仓内湿度数据
- 环境传感器：外界温湿度、气压等
- 设备状态：通风、制冷设备运行状态
- 操作记录：人工操作日志

**输出**：标准化的多维时序数据

#### 2.2.2 数据预处理阶段
**功能**：
- 数据清洗：去除异常值和噪声
- 数据插值：处理缺失数据
- 数据同步：统一时间戳和采样频率
- 质量评估：评估数据可靠性

**输出**：高质量的特征工程输入数据

#### 2.2.3 异常检测阶段
**功能**：识别9大类异常现象
**方法**：基于统计分析和机器学习的异常检测
**输出**：
- 异常类型标识
- 异常严重程度
- 异常空间位置
- 异常时间信息
- 检测置信度

#### 2.2.4 根本原因分析阶段
**输入**：异常检测结果 + 多源数据
**功能**：分析导致异常的根本原因
**方法**：因果推理和关联分析
**输出**：
- 根本原因分类（环境/品质/设备/操作）
- 原因置信度评估
- 因果关系强度
- 时间关联分析

#### 2.2.5 综合诊断阶段
**功能**：整合异常现象和根本原因，形成完整诊断
**输出**：
- 综合诊断报告
- 风险等级评估
- 影响范围分析
- 发展趋势预测

#### 2.2.6 针对性建议阶段
**功能**：基于异常类型和根本原因生成建议
**策略**：
- 治标建议：针对异常现象的应急处理
- 治本建议：针对根本原因的根治措施
- 预防建议：避免类似问题再次发生

#### 2.2.7 执行反馈阶段
**功能**：跟踪建议执行情况和效果
**内容**：
- 执行状态记录
- 效果评估
- 经验积累
- 模型优化反馈

## 3. 重构后的系统架构设计

### 3.1 四模块架构设计

#### 3.1.1 模块一：多维度特征工程引擎
**功能扩展**：
- 原有功能：基于粮温数据的特征计算
- 新增功能：多源数据融合和特征提取

**特征类别**：
```
1. 温度特征（原有）
   - 单点特征：当前温度、温升速率、邻域偏差
   - 聚合特征：区域统计、温度分布、异常比例
   - 空间特征：垂直梯度、径向梯度、层间温差

2. 环境特征（新增）
   - 外界温湿度变化率
   - 气象条件指标
   - 季节性特征
   - 仓内外关联特征

3. 设备特征（新增）
   - 设备运行状态特征
   - 设备性能指标
   - 故障历史特征
   - 维护及时性特征

4. 操作特征（新增）
   - 操作时机特征
   - 操作频次特征
   - 响应时间特征
   - 标准符合度特征

5. 时序特征（增强）
   - 趋势特征
   - 周期性特征
   - 突变点特征
   - 滞后关联特征
```

#### 3.1.2 模块二：AI异常检测模型（重构）
**检测目标**：9大类异常现象
**模型架构**：
```
多任务学习框架：
├─ 共享特征提取层
├─ 空间异常检测分支（4类）
├─ 时间异常检测分支（2类）
├─ 分布异常检测分支（2类）
└─ 数据质量检测分支（1类）
```

**技术方案**：
- 基础模型：梯度提升树（LightGBM）
- 深度学习：Transformer用于时序建模
- 异常检测：Isolation Forest用于无监督检测
- 集成学习：多模型融合提高准确性

#### 3.1.3 模块三：根本原因推理引擎（新增）
**核心功能**：基于异常检测结果分析根本原因

**算法设计**：
```
1. 因果推理层
   - 贝叶斯网络建模
   - 因果图构建
   - 条件概率推理

2. 时序关联层
   - 滞后效应分析
   - 格兰杰因果检验
   - 时间序列因果发现

3. 证据融合层
   - 多源证据整合
   - 不确定性处理
   - 置信度评估

4. 知识推理层
   - 专家规则引擎
   - 案例推理
   - 模式匹配
```

**针对不同异常类型的算法策略**：
```
空间分布异常：
├─ 主要算法：空间聚类分析 + 因果推理
├─ 关键特征：空间梯度、邻域关系、设备分布
└─ 重点原因：设备故障、局部品质问题

时间变化异常：
├─ 主要算法：时序因果分析 + 滞后关联
├─ 关键特征：时间序列模式、外部事件时序
└─ 重点原因：环境变化、操作时机

分布均匀性异常：
├─ 主要算法：统计分析 + 物理建模
├─ 关键特征：分布统计量、通风效果
└─ 重点原因：通风不均、仓房结构

数据质量异常：
├─ 主要算法：设备诊断 + 数据挖掘
├─ 关键特征：数据质量指标、设备状态
└─ 重点原因：设备故障、维护问题
```

#### 3.1.4 模块四：智能建议生成器（重构）
**生成策略**：
```
双重建议机制：
├─ 治标建议：基于异常现象的应急处理
│  ├─ 表层温度异常 → 表层通风、温度监控
│  ├─ 温升速率异常 → 立即通风、降温措施
│  └─ 分布异常 → 调整通风策略
└─ 治本建议：基于根本原因的根治措施
   ├─ 环境因素 → 环境控制、隔热改善
   ├─ 品质问题 → 品质检测、处理措施
   ├─ 设备故障 → 设备维修、更换计划
   └─ 操作问题 → 流程优化、培训计划
```

### 3.2 数据流设计
```
多源数据输入 → 特征工程引擎 → 异常检测模型 → 根本原因推理 → 建议生成
     ↓              ↓              ↓              ↓           ↓
   数据质量评估   特征重要性分析   异常置信度评估   原因置信度评估  建议优先级排序
     ↓              ↓              ↓              ↓           ↓
   质量报告       特征解释        异常报告        原因分析报告   执行建议
```

### 3.3 模块间接口设计
```python
# 异常检测模块输出接口
class AnomalyDetectionResult:
    anomaly_type: str           # 异常类型（9种之一）
    severity_level: float       # 严重程度 [0-1]
    confidence: float           # 检测置信度 [0-1]
    spatial_location: dict      # 空间位置信息
    temporal_info: dict         # 时间信息
    affected_area: str          # 影响范围描述
    key_features: dict          # 关键特征值

# 根本原因分析模块输出接口
class RootCauseAnalysisResult:
    primary_cause: str          # 主要原因类型
    cause_confidence: float     # 原因置信度 [0-1]
    contributing_factors: list  # 贡献因素列表
    causal_strength: float      # 因果关系强度
    temporal_correlation: dict  # 时间关联信息
    evidence_summary: dict      # 证据汇总

# 建议生成模块输出接口
class ActionRecommendation:
    immediate_actions: list     # 立即行动建议
    root_cause_actions: list    # 根本原因处理建议
    preventive_measures: list   # 预防措施建议
    priority_order: list        # 优先级排序
    expected_outcome: str       # 预期效果
    resource_requirements: dict # 资源需求
```
