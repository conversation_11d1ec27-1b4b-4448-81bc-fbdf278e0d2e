# 重构版粮情分析系统需求分析文档

## 1. 异常分类体系重新分析与定义

### 1.1 现有18种异常类型的分类逻辑问题

#### 1.1.1 分类维度混乱问题
**发现的问题**：
现有的18种异常类型混合了多个分类维度：
- **现象维度**：表层局部发热、中部局部发热、底部局部发热
- **原因维度**：硬件异常-点位、硬件异常-线缆
- **程度维度**：温升速率异常、超最高温
- **空间维度**：同层/圈不均、外圈粮温过高

**核心问题**：设备异常导致的温度异常，既可以归类为"设备异常"，也可以归类为"温度异常"，存在分类边界模糊的问题。

#### 1.1.2 表象与根因混淆问题
**现状分析**：
```
混淆示例：
- "硬件异常-点位" → 这是根本原因，不是异常现象
- "表层局部发热" → 这是异常现象，不是根本原因
- "新粮入仓后熟" → 这既包含现象又包含原因
```

**问题本质**：没有明确区分"异常现象"（What）和"异常原因"（Why）。

### 1.2 重新定义异常分类体系

#### 1.2.1 分类原则
1. **现象导向**：异常检测只识别"现象"，不判断"原因"
2. **可观测性**：所有异常类型都应该是可以通过传感器数据直接观测到的现象
3. **互斥性**：不同异常类型之间应该相互独立，避免重叠
4. **完备性**：覆盖所有可能的粮情异常现象

#### 1.2.2 重新设计的异常分类体系

基于保持现象详细程度和业务区分度的原则，重新设计异常分类体系：

**维度一：空间分布异常（Spatial Distribution Anomalies）**
```
1. 表层局部发热 (Surface Local Heating)
   - 现象：表层局部区域温度显著高于周围区域
   - 业务价值：局部问题，可能是局部水分超标、虫害或密封问题
   - 处置策略：局部检查、局部处理、防止扩散

2. 表层整体发热 (Surface Overall Heating)
   - 现象：表层整体温度高于安全阈值
   - 业务价值：整体问题，可能是环境因素或整体品质问题
   - 处置策略：整体通风、环境控制、全面检查

3. 中部局部发热 (Middle Local Heating)
   - 现象：中部局部区域温度异常升高
   - 业务价值：内部局部问题，检查和处理难度较大
   - 处置策略：深度检查、可能需要扒粮处理

4. 中部整体发热 (Middle Overall Heating)
   - 现象：中部整体温度异常升高
   - 业务价值：内部整体问题，风险较高
   - 处置策略：全面通风、可能需要大范围处理

5. 底部局部发热 (Bottom Local Heating)
   - 现象：底部局部区域温度异常升高
   - 业务价值：底部问题，可能与地面湿度、密封相关
   - 处置策略：检查地面密封、底部通风

6. 底部整体发热 (Bottom Overall Heating)
   - 现象：底部整体温度异常升高
   - 业务价值：底部整体问题，可能需要大规模处理
   - 处置策略：底部通风系统检查、整体处理

7. 垂直局部发热 (Vertical Local Heating)
   - 现象：垂直方向局部温度梯度异常
   - 业务价值：垂直通风问题或局部品质问题
   - 处置策略：垂直通风调整、局部检查

8. 垂直整体发热 (Vertical Overall Heating)
   - 现象：整个垂直剖面温度异常
   - 业务价值：垂直通风系统问题或整体品质问题
   - 处置策略：垂直通风系统全面检查
```

**维度二：时间变化异常（Temporal Change Anomalies）**
```
9. 温升速率异常 (Temperature Rise Rate Anomaly)
   - 现象：温度上升速率超过正常范围
   - 业务价值：动态变化监控，预警作用
   - 处置策略：立即通风、密切监控

10. 超最高温 (Exceeding Maximum Temperature)
    - 现象：温度超过安全阈值上限
    - 业务价值：绝对温度控制，安全红线
    - 处置策略：紧急降温、品质检测
```

**维度三：分布均匀性异常（Distribution Uniformity Anomalies）**
```
11. 同层/圈不均 (Same Layer/Ring Unevenness)
    - 现象：同一水平层内温度分布不均匀
    - 业务价值：水平通风效果评估
    - 处置策略：调整水平通风策略

12. 外圈粮温过高 (Outer Ring Temperature Too High)
    - 现象：仓房外圈区域温度过高
    - 业务价值：边缘效应监控，可能与密封、隔热相关
    - 处置策略：检查仓房密封、改善隔热

13. 仓内温差过大 (Excessive Temperature Difference)
    - 现象：仓内不同区域温差超过安全范围
    - 业务价值：整体温度均匀性评估
    - 处置策略：优化通风分布、改善温度均匀性

14. 整仓发热 (Whole Silo Heating)
    - 现象：整个仓房温度整体升高
    - 业务价值：整体风险评估，最高级别告警
    - 处置策略：全面应急处理、可能需要出仓
```

#### 1.2.3 异常类型调整的具体理由

**最终保留的异常现象（14种）及理由**：

1. **空间分布异常保持详细分类（8种）**：
   - 局部vs整体：成因不同（局部可能是点源问题，整体可能是系统性问题）
   - 不同层次：处理难度和策略差异巨大（表层易处理，底部难处理）
   - 垂直方向：反映通风系统效果，需要专门的处理策略

2. **时间变化异常细分保留（2种）**：
   - 温升速率异常：动态监控，预警性质
   - 超最高温：绝对阈值，安全红线

3. **分布均匀性异常详细保留（4种）**：
   - 同层不均：水平通风问题
   - 外圈过高：边缘效应，密封隔热问题
   - 温差过大：整体均匀性问题
   - 整仓发热：最高级别风险

**移除的异常类型及移除理由**：

1. **硬件异常-点位、硬件异常-线缆**：
   - 移除理由：这些是导致数据异常的根本原因，而非可观测的温度分布异常现象
   - 新归属：作为根本原因分析中的"硬件故障"类别

2. **新粮入仓后熟**：
   - 移除理由：这是正常的生理过程，而非异常现象
   - 新归属：作为根本原因分析中的"新粮后熟过程"独立类别

**从18种调整为14种的逻辑**：
- 保持所有真正的温度分布异常现象（14种）
- 将硬件故障和新粮后熟移至根本原因分析模块
- 确保异常检测专注于可观测的温度模式识别

### 1.3 异常现象与根本原因的边界定义

#### 1.3.1 异常现象（What）- 异常检测模块负责
**定义**：通过传感器数据可以直接观测到的温度分布或变化模式的偏离
**特征**：
- 可量化：有明确的数值指标和阈值
- 可观测：基于现有传感器数据可以检测
- 现象性：描述"是什么"而不是"为什么"

#### 1.3.2 根本原因（Why）- 根本原因分析模块负责
**定义**：导致异常现象发生的深层次原因
**分类**：
- 环境因素：外界温湿度、气象条件、季节性影响等
- 粮食品质变化：水分超标、虫害活动、霉变、呼吸作用等
- 设备故障：通风系统、制冷系统、密封系统等设备问题
- 操作管理因素：通风时机、维护计划、响应速度等人为因素
- 硬件故障：传感器故障、线缆故障、数据采集系统故障等
- 新粮后熟过程：新入仓粮食的正常生理活动导致的温升

#### 1.3.3 边界划分示例
```
异常现象：表层温度异常
├─ 现象描述：表层区域温度高于安全阈值
├─ 检测方法：基于温度传感器数据的统计分析
└─ 可能的根本原因：
   ├─ 环境因素：外界高温、太阳直射
   ├─ 粮食品质：表层水分过高、虫害活动
   ├─ 设备故障：仓顶密封不良、通风不畅
   └─ 操作管理：通风时机不当、检查不及时
```

## 2. 正确的业务流程定义

### 2.1 完整业务流程
```
数据采集 → 数据预处理 → 异常检测 → 根本原因分析 → 综合诊断 → 针对性建议 → 执行反馈 → 效果评估
```

### 2.2 各阶段详细定义

#### 2.2.1 数据采集阶段
**输入**：多源传感器数据
- 温度传感器：粮温分布数据
- 湿度传感器：仓内湿度数据
- 环境传感器：外界温湿度、气压等
- 设备状态：通风、制冷设备运行状态
- 操作记录：人工操作日志

**输出**：标准化的多维时序数据

#### 2.2.2 数据预处理阶段
**功能**：
- 数据清洗：去除异常值和噪声
- 数据插值：处理缺失数据
- 数据同步：统一时间戳和采样频率
- 质量评估：评估数据可靠性

**输出**：高质量的特征工程输入数据

#### 2.2.3 异常检测阶段
**功能**：识别9大类异常现象
**方法**：基于统计分析和机器学习的异常检测
**输出**：
- 异常类型标识
- 异常严重程度
- 异常空间位置
- 异常时间信息
- 检测置信度

#### 2.2.4 根本原因分析阶段
**输入**：异常检测结果 + 多源数据
**功能**：分析导致异常的根本原因
**方法**：因果推理和关联分析
**输出**：
- 根本原因分类（环境/品质/设备/操作）
- 原因置信度评估
- 因果关系强度
- 时间关联分析

#### 2.2.5 综合诊断阶段
**功能**：整合异常现象和根本原因，形成完整诊断
**输出**：
- 综合诊断报告
- 风险等级评估
- 影响范围分析
- 发展趋势预测

#### 2.2.6 针对性建议阶段
**功能**：基于异常类型和根本原因生成建议
**策略**：
- 治标建议：针对异常现象的应急处理
- 治本建议：针对根本原因的根治措施
- 预防建议：避免类似问题再次发生

#### 2.2.7 执行反馈阶段
**功能**：跟踪建议执行情况和效果
**内容**：
- 执行状态记录
- 效果评估
- 经验积累
- 模型优化反馈

## 3. 重构后的系统架构设计

### 3.1 四模块架构设计

#### 3.1.1 模块一：多维度特征工程引擎
**功能扩展**：
- 原有功能：基于粮温数据的特征计算
- 新增功能：多源数据融合和特征提取

**特征体系设计**：基于"点-线-面-体"四个维度全面刻画粮情

### 3.1.1.1 单点特征 (Point-level Features)
针对每个测温点 i 在时刻 t 计算的特征：

```python
class PointLevelFeatures:
    # 基础温度特征
    T_current: float                # T_i(t) - 当前绝对温度
    T_max_24h: float               # max(T_i(t-24h:t)) - 24小时最高温
    T_min_24h: float               # min(T_i(t-24h:t)) - 24小时最低温
    T_mean_7d: float               # mean(T_i(t-7d:t)) - 7天平均温度

    # 温升速率特征
    rate_1h: float                 # T_i(t) - T_i(t-1h) - 1小时温升
    rate_6h: float                 # T_i(t) - T_i(t-6h) - 6小时温升
    rate_24h: float                # T_i(t) - T_i(t-24h) - 24小时温升
    rate_7d: float                 # T_i(t) - T_i(t-7d) - 7天温升

    # 空间关系特征
    neighbor_deviation: float       # T_i(t) - mean(T_neighbors(t)) - 与邻域偏差
    layer_deviation: float          # T_i(t) - mean(T_layer(t)) - 与同层偏差
    region_deviation: float         # T_i(t) - mean(T_region(t)) - 与区域偏差

    # 稳定性特征
    volatility_24h: float          # std(T_i(t-24h:t)) - 24小时波动性
    volatility_7d: float           # std(T_i(t-7d:t)) - 7天波动性
    trend_consistency: float        # 温升趋势一致性指标

    # 异常检测特征
    is_local_hotspot: bool         # 是否为局部热点
    hotspot_intensity: float       # 热点强度（相对邻域的温度偏差）
    anomaly_score: float           # 基于历史模式的异常评分
```

### 3.1.1.2 聚合特征 (Aggregate-level Features)
按区域（层、圈、垂直区域、全仓）进行聚合计算：

```python
class AggregateLevelFeatures:
    # 区域定义
    REGIONS = {
        'surface': 'Z坐标 > 粮堆高度的80%',      # 表层
        'middle': '20% < Z坐标 < 80%',           # 中部
        'bottom': 'Z坐标 < 20%',                 # 底部
        'outer_ring': '径向坐标 > 仓储半径的70%', # 外圈
        'inner_ring': '径向坐标 < 30%',          # 内圈
        'whole_silo': '全仓范围'                 # 整仓
    }

    # 基础统计特征
    region_T_max: float            # max(T_region(t)) - 区域最高温
    region_T_min: float            # min(T_region(t)) - 区域最低温
    region_T_mean: float           # mean(T_region(t)) - 区域平均温
    region_T_median: float         # median(T_region(t)) - 区域中位温度
    region_T_std: float            # std(T_region(t)) - 区域温度标准差
    region_T_range: float          # max - min - 区域温度范围

    # 分布特征
    region_high_temp_ratio: float  # count(T_i > 阈值) / count(T_i) - 高温点占比
    region_hotspot_count: int      # 区域内热点数量
    region_hotspot_area: float     # 热点面积占区域面积比例

    # 变化特征
    region_rate_mean: float        # mean(rate_24h) - 区域平均温升速率
    region_rate_max: float         # max(rate_24h) - 区域最大温升速率
    region_rate_std: float         # std(rate_24h) - 温升速率标准差

    # 均匀性特征
    temperature_uniformity: float   # 温度分布均匀性指数
    spatial_autocorr: float        # 空间自相关系数
    cluster_coefficient: float     # 聚类系数
```

### 3.1.1.3 空间梯度特征 (Spatial-Gradient Features)
计算不同区域间的温差关系：

```python
class SpatialGradientFeatures:
    # 垂直梯度特征
    vertical_gradient: float       # Mean(T_surface) - Mean(T_bottom) - 垂直温差
    surface_middle_diff: float     # Mean(T_surface) - Mean(T_middle) - 表中温差
    middle_bottom_diff: float      # Mean(T_middle) - Mean(T_bottom) - 中底温差
    max_layer_diff: float          # max(Mean(T_layer)) - min(Mean(T_layer)) - 最大层间温差

    # 径向梯度特征
    radial_gradient: float         # Mean(T_outer) - Mean(T_inner) - 径向温差
    center_edge_ratio: float       # Mean(T_center) / Mean(T_edge) - 中心边缘比

    # 方向性特征
    north_south_diff: float        # 南北方向温差
    east_west_diff: float          # 东西方向温差
    max_directional_diff: float    # 最大方向性温差

    # 梯度变化特征
    gradient_change_24h: float     # 24小时梯度变化
    gradient_stability: float     # 梯度稳定性指标
```

### 3.1.1.4 时序特征 (Temporal Features)
基于时间序列分析的特征：

```python
class TemporalFeatures:
    # 趋势特征
    linear_trend_slope: float      # 线性趋势斜率
    trend_strength: float          # 趋势强度
    trend_direction: int           # 趋势方向（上升/下降/平稳）

    # 周期性特征
    daily_pattern_strength: float  # 日周期模式强度
    weekly_pattern_strength: float # 周周期模式强度
    seasonal_component: float      # 季节性成分

    # 突变检测特征
    change_point_count: int        # 突变点数量
    max_change_magnitude: float    # 最大突变幅度
    change_point_recency: float    # 最近突变点距离

    # 自相关特征
    autocorr_lag1: float          # 1阶自相关
    autocorr_lag24: float         # 24小时滞后自相关
    autocorr_lag168: float        # 7天滞后自相关

    # 复杂性特征
    entropy: float                # 时序熵
    fractal_dimension: float      # 分形维数
    hurst_exponent: float         # Hurst指数
```

### ******* 多源数据融合特征 (Multi-source Features)
整合环境、设备、操作等多源数据：

```python
class MultiSourceFeatures:
    # 环境关联特征
    indoor_outdoor_temp_corr: float    # 室内外温度相关性
    weather_impact_score: float       # 天气影响评分
    seasonal_deviation: float         # 季节性偏差
    humidity_temp_relationship: float # 湿度温度关系

    # 设备状态特征
    ventilation_efficiency: float     # 通风效率
    cooling_performance: float        # 制冷性能
    equipment_health_score: float     # 设备健康评分
    maintenance_overdue_days: int     # 维护超期天数

    # 操作质量特征
    operation_timeliness: float       # 操作及时性
    response_delay: float             # 响应延迟
    standard_compliance: float        # 标准符合度
    operator_experience: float        # 操作员经验水平

    # 数据质量特征
    data_completeness: float          # 数据完整性
    sensor_reliability: float        # 传感器可靠性
    measurement_accuracy: float       # 测量精度
    data_consistency: float          # 数据一致性
```

### ******* 特征重要性分析
基于业务知识和统计分析确定特征重要性：

```python
class FeatureImportance:
    # 核心特征（权重 > 0.8）
    CORE_FEATURES = [
        'T_current',           # 当前温度
        'rate_24h',           # 24小时温升
        'neighbor_deviation',  # 邻域偏差
        'region_T_std',       # 区域温度标准差
        'vertical_gradient'    # 垂直温度梯度
    ]

    # 重要特征（权重 0.5-0.8）
    IMPORTANT_FEATURES = [
        'volatility_7d',      # 7天波动性
        'region_high_temp_ratio', # 高温点占比
        'trend_strength',     # 趋势强度
        'hotspot_intensity'   # 热点强度
    ]

    # 辅助特征（权重 0.2-0.5）
    AUXILIARY_FEATURES = [
        'seasonal_component', # 季节性成分
        'equipment_health_score', # 设备健康评分
        'data_completeness'   # 数据完整性
    ]
```

#### 3.1.2 模块二：AI异常检测模型（重构）
**检测目标**：14类异常现象
**模型架构**：
```
多任务学习框架：
├─ 共享特征提取层
├─ 空间异常检测分支（8类）
│  ├─ 表层局部发热
│  ├─ 表层整体发热
│  ├─ 中部局部发热
│  ├─ 中部整体发热
│  ├─ 底部局部发热
│  ├─ 底部整体发热
│  ├─ 垂直局部发热
│  └─ 垂直整体发热
├─ 时间异常检测分支（2类）
│  ├─ 温升速率异常
│  └─ 超最高温
└─ 分布异常检测分支（4类）
   ├─ 同层/圈不均
   ├─ 外圈粮温过高
   ├─ 仓内温差过大
   └─ 整仓发热
```

**技术方案**：
- 基础模型：梯度提升树（LightGBM）
- 深度学习：Transformer用于时序建模
- 异常检测：Isolation Forest用于无监督检测
- 集成学习：多模型融合提高准确性

#### 3.1.3 模块三：根本原因推理引擎（新增）
**核心功能**：基于异常检测结果分析根本原因

**算法设计**：
```
1. 因果推理层
   - 贝叶斯网络建模
   - 因果图构建
   - 条件概率推理

2. 时序关联层
   - 滞后效应分析
   - 格兰杰因果检验
   - 时间序列因果发现

3. 证据融合层
   - 多源证据整合
   - 不确定性处理
   - 置信度评估

4. 知识推理层
   - 专家规则引擎
   - 案例推理
   - 模式匹配
```

**针对14种异常类型的算法策略**：

**空间分布异常（8类）**：
```
表层局部发热：
├─ 算法：局部异常检测 + 空间聚类 + 因果推理
├─ 特征：局部温度梯度、热点面积、邻域温差
├─ 重点原因：局部水分超标、虫害活动、密封问题
└─ 处理策略：局部检查、防止扩散

表层整体发热：
├─ 算法：整体统计分析 + 环境关联分析
├─ 特征：整体温度水平、环境相关性、时间模式
├─ 重点原因：环境因素、整体品质问题、通风不当
└─ 处理策略：整体通风、环境控制

中部局部发热：
├─ 算法：深度异常检测 + 传播模式分析
├─ 特征：内部温度分布、传播路径、持续时间
├─ 重点原因：内部品质变化、局部虫害
└─ 处理策略：深度检查、可能需要扒粮

中部整体发热：
├─ 算法：整体趋势分析 + 多因子关联
├─ 特征：整体温升趋势、多层温度关联
├─ 重点原因：整体品质问题、通风系统故障
└─ 处理策略：全面通风、大范围处理

底部局部发热：
├─ 算法：底部特征分析 + 地面因素关联
├─ 特征：底部温度分布、地面湿度、密封状态
├─ 重点原因：地面湿度、底部密封问题
└─ 处理策略：底部密封检查、底部通风

底部整体发热：
├─ 算法：底部系统分析 + 结构因素评估
├─ 特征：底部整体温度、结构完整性
├─ 重点原因：底部通风系统问题、结构问题
└─ 处理策略：底部系统全面检查

垂直局部发热：
├─ 算法：垂直梯度分析 + 通风效果评估
├─ 特征：垂直温度梯度、通风路径、局部阻塞
├─ 重点原因：垂直通风局部问题、局部阻塞
└─ 处理策略：垂直通风调整、局部疏通

垂直整体发热：
├─ 算法：垂直系统分析 + 整体通风评估
├─ 特征：整体垂直温度分布、通风系统状态
├─ 重点原因：垂直通风系统故障、整体品质问题
└─ 处理策略：垂直通风系统全面检查
```

**时间变化异常（2类）**：
```
温升速率异常：
├─ 算法：时序分析 + 变化率检测 + 滞后关联
├─ 特征：温升速率、加速度、时间模式
├─ 重点原因：品质变化、环境突变、操作不当、新粮后熟过程
└─ 处理策略：立即通风、密切监控、原因识别

超最高温：
├─ 算法：阈值检测 + 紧急评估 + 风险分析
├─ 特征：绝对温度值、超标程度、影响范围
├─ 重点原因：严重品质问题、设备故障、环境极端
└─ 处理策略：紧急降温、品质检测、风险评估
```

**分布均匀性异常（4类）**：
```
同层/圈不均：
├─ 算法：水平分布分析 + 通风效果评估
├─ 特征：同层温度方差、径向分布、通风均匀性
├─ 重点原因：水平通风不均、仓房结构问题
└─ 处理策略：调整水平通风策略、改善分布

外圈粮温过高：
├─ 算法：边缘效应分析 + 隔热性能评估
├─ 特征：边缘温度、内外温差、隔热效果
├─ 重点原因：隔热不良、密封问题、外界影响
└─ 处理策略：改善隔热、检查密封、边缘通风

仓内温差过大：
├─ 算法：全局分布分析 + 均匀性评估
├─ 特征：全局温度分布、温差统计、均匀性指标
├─ 重点原因：通风分布不均、仓房设计问题
└─ 处理策略：优化通风分布、改善均匀性

整仓发热：
├─ 算法：整体风险评估 + 紧急响应分析
├─ 特征：整体温度水平、发热强度、发展趋势
├─ 重点原因：整体品质严重问题、系统性故障
└─ 处理策略：全面应急处理、考虑出仓
```

**扩展的根本原因分析体系**：

基于14种异常现象，建立6大类根本原因的详细分析体系：

```
1. 环境因素分析
   ├─ 外界温湿度影响
   │  ├─ 特征：室内外温度相关性、滞后效应
   │  ├─ 算法：相关性分析、滞后关联分析
   │  └─ 判定：相关系数 > 0.7，滞后时间 < 6小时
   ├─ 气象条件变化
   │  ├─ 特征：风速、湿度、气压变化
   │  ├─ 算法：气象数据关联分析
   │  └─ 判定：气象变化与温度异常时间吻合
   ├─ 季节性因素
   │  ├─ 特征：季节模式匹配度、历史同期对比
   │  ├─ 算法：季节性分解、模式匹配
   │  └─ 判定：季节模式匹配度 > 0.8
   └─ 太阳辐射影响
      ├─ 特征：太阳辐射强度、方向性温差
      ├─ 算法：辐射强度关联分析
      └─ 判定：辐射强度 > 800W/m²，方向性温差明显

2. 粮食品质变化分析
   ├─ 水分含量超标
   │  ├─ 特征：水分检测值、持续温升天数
   │  ├─ 算法：水分阈值检测、温升模式分析
   │  └─ 判定：水分 > 14%，持续温升 > 7天
   ├─ 虫害活动
   │  ├─ 特征：局部热点聚集、CO2浓度
   │  ├─ 算法：热点聚类分析、生物活动指标
   │  └─ 判定：热点聚集度 > 0.7，CO2 > 1000ppm
   ├─ 霉菌生长
   │  ├─ 特征：湿度条件、温度适宜性
   │  ├─ 算法：霉菌生长条件评估
   │  └─ 判定：湿度 > 70%，温度20-30°C
   ├─ 呼吸作用增强
   │  ├─ 特征：CO2浓度变化、温升模式
   │  ├─ 算法：呼吸强度计算、代谢活动评估
   │  └─ 判定：CO2增长率 > 50ppm/天
   └─ 生物化学反应
      ├─ 特征：温度变化模式、化学指标
      ├─ 算法：反应动力学建模
      └─ 判定：指数型温升模式，反应速率常数异常

3. 设备故障分析
   ├─ 通风系统故障
   │  ├─ 特征：通风效率、气流分布
   │  ├─ 算法：通风效果评估、流场分析
   │  └─ 判定：通风效率 < 70%，分布不均
   ├─ 制冷系统故障
   │  ├─ 特征：制冷性能、温控效果
   │  ├─ 算法：制冷效率计算、温控分析
   │  └─ 判定：制冷效率 < 80%，温控失效
   ├─ 密封系统故障
   │  ├─ 特征：密封完整性、边缘温度
   │  ├─ 算法：密封性能评估、边缘效应分析
   │  └─ 判定：密封完整性 < 90%，边缘温度异常
   └─ 控制系统故障
      ├─ 特征：控制精度、响应时间
      ├─ 算法：控制系统性能分析
      └─ 判定：控制精度偏差 > 10%，响应延迟 > 30分钟
```

```
4. 操作管理因素分析
   ├─ 通风时机不当
   │  ├─ 特征：通风时机偏差、气象条件匹配
   │  ├─ 算法：时机优化分析、气象关联评估
   │  └─ 判定：时机偏差 > 4小时，气象条件不匹配
   ├─ 维护计划延误
   │  ├─ 特征：维护超期天数、设备性能下降
   │  ├─ 算法：维护计划符合度分析
   │  └─ 判定：维护超期 > 30天，性能下降 > 20%
   ├─ 响应速度缓慢
   │  ├─ 特征：异常发现到处理的时间间隔
   │  ├─ 算法：响应时间分析、处理效率评估
   │  └─ 判定：响应时间 > 24小时，处理延迟
   ├─ 操作标准偏差
   │  ├─ 特征：操作标准符合度、操作记录
   │  ├─ 算法：标准符合度评估、偏差分析
   │  └─ 判定：符合度 < 80%，偏差频繁
   └─ 人员经验不足
      ├─ 特征：操作员经验水平、培训完成率
      ├─ 算法：经验水平评估、能力分析
      └─ 判定：经验水平 < 60%，培训不足

5. 硬件故障分析（新增）
   ├─ 传感器点位故障
   │  ├─ 特征：单点数据异常、邻点对比、历史稳定性
   │  ├─ 算法：单点异常检测、传感器诊断
   │  └─ 判定：数据漂移 > 5°C，稳定性 < 95%
   ├─ 线缆连接故障
   │  ├─ 特征：信号强度、连接稳定性、传输质量
   │  ├─ 算法：连接诊断、信号质量分析
   │  └─ 判定：信号强度 < 80%，连接不稳定
   ├─ 数据采集故障
   │  ├─ 特征：数据完整性、采集频率、系统状态
   │  ├─ 算法：数据质量评估、系统诊断
   │  └─ 判定：数据完整性 < 90%，采集异常
   ├─ 通信系统故障
   │  ├─ 特征：通信延迟、数据丢包、网络状态
   │  ├─ 算法：网络性能分析、通信质量评估
   │  └─ 判定：延迟 > 5秒，丢包率 > 5%
   └─ 校准偏差问题
      ├─ 特征：校准偏差、测量精度、标准对比
      ├─ 算法：校准精度分析、偏差检测
      └─ 判定：偏差 > 2°C，精度下降 > 10%

6. 新粮后熟过程分析（新增）
   ├─ 正常生理活动
   │  ├─ 特征：入仓时间、温升模式、生理指标
   │  ├─ 算法：新粮模式识别、生理过程建模
   │  └─ 判定：入仓 < 30天，温升符合生理模式
   ├─ 水分平衡调整
   │  ├─ 特征：水分变化、平衡过程、调整速率
   │  ├─ 算法：水分平衡建模、调整过程分析
   │  └─ 判定：水分缓慢下降，平衡趋势明显
   ├─ 呼吸强度变化
   │  ├─ 特征：呼吸强度、CO2变化、代谢活动
   │  ├─ 算法：呼吸强度建模、代谢分析
   │  └─ 判定：呼吸强度逐渐降低，代谢趋于稳定
   ├─ 温度自然调节
   │  ├─ 特征：温度调节模式、自然降温、稳定趋势
   │  ├─ 算法：温度调节建模、稳定性分析
   │  └─ 判定：温度逐渐稳定，调节模式正常
   └─ 品质稳定过程
      ├─ 特征：品质指标变化、稳定性趋势
      ├─ 算法：品质稳定性建模、趋势分析
      └─ 判定：品质指标趋于稳定，无恶化趋势
```

#### 3.1.4 模块四：智能建议生成器（重构）
**生成策略**：
```
双重建议机制：
├─ 治标建议：基于异常现象的应急处理
│  ├─ 表层温度异常 → 表层通风、温度监控
│  ├─ 温升速率异常 → 立即通风、降温措施
│  └─ 分布异常 → 调整通风策略
└─ 治本建议：基于根本原因的根治措施
   ├─ 环境因素 → 环境控制、隔热改善
   ├─ 品质问题 → 品质检测、处理措施
   ├─ 设备故障 → 设备维修、更换计划
   ├─ 操作问题 → 流程优化、培训计划
   ├─ 硬件故障 → 硬件检修、系统升级
   └─ 新粮后熟 → 耐心等待、适度调控
```

### 3.2 数据流设计
```
多源数据输入 → 特征工程引擎 → 异常检测模型 → 根本原因推理 → 建议生成
     ↓              ↓              ↓              ↓           ↓
   数据质量评估   特征重要性分析   异常置信度评估   原因置信度评估  建议优先级排序
     ↓              ↓              ↓              ↓           ↓
   质量报告       特征解释        异常报告        原因分析报告   执行建议
```

### 3.3 模块间接口设计
```python
# 异常检测模块输出接口
class AnomalyDetectionResult:
    anomaly_type: str           # 异常类型（14种之一）
    severity_level: float       # 严重程度 [0-1]
    confidence: float           # 检测置信度 [0-1]
    spatial_location: dict      # 空间位置信息
    temporal_info: dict         # 时间信息
    affected_area: str          # 影响范围描述
    key_features: dict          # 关键特征值

# 根本原因分析模块输出接口
class RootCauseAnalysisResult:
    primary_cause: str          # 主要原因类型
    cause_confidence: float     # 原因置信度 [0-1]
    contributing_factors: list  # 贡献因素列表
    causal_strength: float      # 因果关系强度
    temporal_correlation: dict  # 时间关联信息
    evidence_summary: dict      # 证据汇总

# 建议生成模块输出接口
class ActionRecommendation:
    immediate_actions: list     # 立即行动建议
    root_cause_actions: list    # 根本原因处理建议
    preventive_measures: list   # 预防措施建议
    priority_order: list        # 优先级排序
    expected_outcome: str       # 预期效果
    resource_requirements: dict # 资源需求
```
