# 重构版粮情分析系统需求分析文档

## 1. 异常分类体系重新分析与定义

### 1.1 现有18种异常类型的分类逻辑问题

#### 1.1.1 分类维度混乱问题
**发现的问题**：
现有的18种异常类型混合了多个分类维度：
- **现象维度**：表层局部发热、中部局部发热、底部局部发热
- **原因维度**：硬件异常-点位、硬件异常-线缆
- **程度维度**：温升速率异常、超最高温
- **空间维度**：同层/圈不均、外圈粮温过高

**核心问题**：设备异常导致的温度异常，既可以归类为"设备异常"，也可以归类为"温度异常"，存在分类边界模糊的问题。

#### 1.1.2 表象与根因混淆问题
**现状分析**：
```
混淆示例：
- "硬件异常-点位" → 这是根本原因，不是异常现象
- "表层局部发热" → 这是异常现象，不是根本原因
- "新粮入仓后熟" → 这既包含现象又包含原因
```

**问题本质**：没有明确区分"异常现象"（What）和"异常原因"（Why）。

### 1.2 重新定义异常分类体系

#### 1.2.1 分类原则
1. **现象导向**：异常检测只识别"现象"，不判断"原因"
2. **可观测性**：所有异常类型都应该是可以通过传感器数据直接观测到的现象
3. **互斥性**：不同异常类型之间应该相互独立，避免重叠
4. **完备性**：覆盖所有可能的粮情异常现象

#### 1.2.2 重新设计的异常分类体系

基于异常本质特征的科学分类，重新设计异常分类体系：

**分类原则重新确立**：
1. **本质特征导向**：基于异常的本质特征进行分类，而非表面现象
2. **逻辑互斥性**：不同分类维度之间逻辑清晰，相互独立
3. **完备性**：覆盖所有可能的粮情异常现象
4. **可操作性**：每种异常类型都有明确的检测方法和处置策略

**重新设计的四维分类体系**：

**维度一：空间局部性异常（Spatial Locality Anomalies）**
基于异常的空间分布特征进行分类
```
1. 表层局部发热 (Surface Local Heating)
   - 本质特征：表层空间局部性温度异常
   - 检测特征：局部热点、邻域温差、热点面积
   - 业务价值：早期发现局部问题，防止扩散
   - 处置策略：局部检查、局部处理、防止扩散

2. 中部局部发热 (Middle Local Heating)
   - 本质特征：中部空间局部性温度异常
   - 检测特征：内部热点、传播路径、持续时间
   - 业务价值：内部局部问题识别，处理难度评估
   - 处置策略：深度检查、可能需要扒粮处理

3. 底部局部发热 (Bottom Local Heating)
   - 本质特征：底部空间局部性温度异常
   - 检测特征：底部热点、地面关联、密封状态
   - 业务价值：底部问题识别，结构性问题排查
   - 处置策略：检查地面密封、底部通风

4. 垂直局部发热 (Vertical Local Heating)
   - 本质特征：垂直方向局部性温度异常
   - 检测特征：垂直梯度异常、局部阻塞、通风路径
   - 业务价值：垂直通风局部问题识别
   - 处置策略：垂直通风调整、局部疏通
```

**维度二：空间整体性异常（Spatial Global Anomalies）**
基于异常的空间整体特征进行分类
```
5. 表层整体发热 (Surface Global Heating)
   - 本质特征：表层空间整体性温度异常
   - 检测特征：表层整体温度水平、环境相关性
   - 业务价值：表层整体问题识别，环境因素评估
   - 处置策略：整体通风、环境控制、全面检查

6. 中部整体发热 (Middle Global Heating)
   - 本质特征：中部空间整体性温度异常
   - 检测特征：中部整体温升、多层关联
   - 业务价值：内部整体问题识别，系统性风险评估
   - 处置策略：全面通风、大范围处理

7. 底部整体发热 (Bottom Global Heating)
   - 本质特征：底部空间整体性温度异常
   - 检测特征：底部整体温度、结构完整性
   - 业务价值：底部整体问题识别，系统性故障排查
   - 处置策略：底部系统全面检查

8. 垂直整体发热 (Vertical Global Heating)
   - 本质特征：垂直方向整体性温度异常
   - 检测特征：整体垂直温度分布、通风系统状态
   - 业务价值：垂直通风系统整体问题识别
   - 处置策略：垂直通风系统全面检查
```

**维度三：温度阈值异常（Temperature Threshold Anomalies）**
基于温度绝对值和变化率的阈值特征进行分类
```
9. 超最高温异常 (Maximum Temperature Exceeded)
   - 本质特征：温度绝对值超过安全阈值
   - 检测特征：绝对温度值、超标程度、影响范围
   - 业务价值：安全红线监控，紧急风险识别
   - 处置策略：紧急降温、品质检测、风险评估

10. 温升速率异常 (Temperature Rise Rate Anomaly)
    - 本质特征：温度变化率超过正常范围
    - 检测特征：温升速率、加速度、时间模式
    - 业务价值：动态变化监控，趋势预警
    - 处置策略：立即通风、密切监控、原因识别
```

**维度四：分布均匀性异常（Distribution Uniformity Anomalies）**
基于温度分布的均匀性特征进行分类
```
11. 水平分布不均 (Horizontal Distribution Unevenness)
    - 本质特征：水平方向温度分布不均匀
    - 检测特征：同层温度方差、径向分布、通风均匀性
    - 业务价值：水平通风效果评估，分布优化
    - 处置策略：调整水平通风策略、改善分布

12. 外圈温度偏高 (Outer Ring Temperature Deviation)
    - 本质特征：外圈区域温度相对偏高
    - 检测特征：边缘温度、内外温差、隔热效果
    - 业务价值：边缘效应监控，隔热性能评估
    - 处置策略：改善隔热、检查密封、边缘通风

13. 全局温差过大 (Global Temperature Range Excessive)
    - 本质特征：全局温度分布范围过大
    - 检测特征：全局温度范围、分布标准差、极值差异
    - 业务价值：整体温度均匀性评估，系统性能评价
    - 处置策略：优化通风分布、改善温度均匀性

14. 整仓温度偏高 (Whole Silo Temperature Elevation)
    - 本质特征：整仓温度水平整体偏高
    - 检测特征：整体温度水平、发热强度、发展趋势
    - 业务价值：整体风险评估，最高级别告警
    - 处置策略：全面应急处理、考虑出仓
```

#### 1.2.3 重新分类的逻辑依据和优势

**重新分类的核心逻辑**：

1. **解决"超最高温"分类问题**：
   - 原问题：被错误归类为"时间变化异常"
   - 解决方案：创建"温度阈值异常"维度，基于温度绝对值特征分类
   - 逻辑依据：超最高温的本质是温度绝对值超标，与时间变化无关

2. **解决"整仓发热"分类问题**：
   - 原问题：被错误归类为"分布均匀性异常"
   - 解决方案：重新定义为"整仓温度偏高"，归入"温度阈值异常"
   - 逻辑依据：整仓发热的本质是整体温度水平偏高，不是分布不均问题

3. **四维分类体系的科学性**：
   ```
   维度一：空间局部性异常（4种）
   ├─ 分类依据：异常的空间局部性特征
   ├─ 核心特征：局部热点、邻域温差、空间聚集
   └─ 业务价值：早期发现、精准定位、防止扩散

   维度二：空间整体性异常（4种）
   ├─ 分类依据：异常的空间整体性特征
   ├─ 核心特征：整体温度水平、区域关联、系统性
   └─ 业务价值：系统性问题识别、整体风险评估

   维度三：温度阈值异常（2种）
   ├─ 分类依据：温度绝对值和变化率的阈值特征
   ├─ 核心特征：绝对温度值、变化速率、阈值超标
   └─ 业务价值：安全红线监控、动态预警

   维度四：分布均匀性异常（4种）
   ├─ 分类依据：温度分布的均匀性特征
   ├─ 核心特征：分布方差、均匀性指数、空间梯度
   └─ 业务价值：通风效果评估、分布优化
   ```

**重新分类的优势**：

1. **逻辑严密性**：
   - 每个维度基于明确的本质特征
   - 维度间相互独立，无逻辑重叠
   - 分类标准统一，便于理解和应用

2. **技术可实现性**：
   - 每种异常类型都有明确的检测特征
   - 特征计算方法清晰，算法实现可行
   - 便于构建多任务学习模型

3. **业务实用性**：
   - 分类与实际处置策略高度对应
   - 便于保管员理解和操作
   - 支持精准的原因分析和建议生成

4. **可扩展性**：
   - 四维框架可容纳新的异常类型
   - 便于系统功能扩展和升级
   - 支持不同粮食品种的适配

**与原分类的对比**：
```
原分类问题 → 新分类解决方案
├─ 超最高温归类错误 → 创建温度阈值异常维度
├─ 整仓发热归类错误 → 重新定义为温度偏高问题
├─ 分类维度混乱 → 建立四维科学分类体系
├─ 逻辑不够严密 → 基于本质特征的逻辑分类
└─ 技术实现困难 → 明确的特征和算法对应关系
```

### 1.3 异常现象与根本原因的边界定义

#### 1.3.1 异常现象（What）- 异常检测模块负责
**定义**：通过传感器数据可以直接观测到的温度分布或变化模式的偏离
**特征**：
- 可量化：有明确的数值指标和阈值
- 可观测：基于现有传感器数据可以检测
- 现象性：描述"是什么"而不是"为什么"

#### 1.3.2 根本原因（Why）- 根本原因分析模块负责
**定义**：导致异常现象发生的深层次原因
**分类**：
- 环境因素：外界温湿度、气象条件、季节性影响等
- 粮食品质变化：水分超标、虫害活动、霉变、呼吸作用等
- 设备故障：通风系统、制冷系统、密封系统等设备问题
- 操作管理因素：通风时机、维护计划、响应速度等人为因素
- 硬件故障：传感器故障、线缆故障、数据采集系统故障等
- 新粮后熟过程：新入仓粮食的正常生理活动导致的温升

#### 1.3.3 边界划分示例
```
异常现象：表层温度异常
├─ 现象描述：表层区域温度高于安全阈值
├─ 检测方法：基于温度传感器数据的统计分析
└─ 可能的根本原因：
   ├─ 环境因素：外界高温、太阳直射
   ├─ 粮食品质：表层水分过高、虫害活动
   ├─ 设备故障：仓顶密封不良、通风不畅
   └─ 操作管理：通风时机不当、检查不及时
```

## 2. 正确的业务流程定义

### 2.1 完整业务流程
```
数据采集 → 数据预处理 → 异常检测 → 根本原因分析 → 综合诊断 → 针对性建议 → 执行反馈 → 效果评估
```

### 2.2 各阶段详细定义

#### 2.2.1 数据采集阶段
**输入**：多源传感器数据
- 温度传感器：粮温分布数据
- 湿度传感器：仓内湿度数据
- 环境传感器：外界温湿度、气压等
- 设备状态：通风、制冷设备运行状态
- 操作记录：人工操作日志

**输出**：标准化的多维时序数据

#### 2.2.2 数据预处理阶段
**功能**：
- 数据清洗：去除异常值和噪声
- 数据插值：处理缺失数据
- 数据同步：统一时间戳和采样频率
- 质量评估：评估数据可靠性

**输出**：高质量的特征工程输入数据

#### 2.2.3 异常检测阶段
**功能**：识别9大类异常现象
**方法**：基于统计分析和机器学习的异常检测
**输出**：
- 异常类型标识
- 异常严重程度
- 异常空间位置
- 异常时间信息
- 检测置信度

#### 2.2.4 根本原因分析阶段
**输入**：异常检测结果 + 多源数据
**功能**：分析导致异常的根本原因
**方法**：因果推理和关联分析
**输出**：
- 根本原因分类（环境/品质/设备/操作）
- 原因置信度评估
- 因果关系强度
- 时间关联分析

#### 2.2.5 综合诊断阶段
**功能**：整合异常现象和根本原因，形成完整诊断
**输出**：
- 综合诊断报告
- 风险等级评估
- 影响范围分析
- 发展趋势预测

#### 2.2.6 针对性建议阶段
**功能**：基于异常类型和根本原因生成建议
**策略**：
- 治标建议：针对异常现象的应急处理
- 治本建议：针对根本原因的根治措施
- 预防建议：避免类似问题再次发生

#### 2.2.7 执行反馈阶段
**功能**：跟踪建议执行情况和效果
**内容**：
- 执行状态记录
- 效果评估
- 经验积累
- 模型优化反馈

## 3. 重构后的系统架构设计

### 3.1 四模块架构设计

#### 3.1.1 模块一：多维度特征工程引擎
**功能扩展**：
- 原有功能：基于粮温数据的特征计算
- 新增功能：多源数据融合和特征提取

**特征体系设计**：基于"点-线-面-体"四个维度全面刻画粮情

### 3.1.1.1 单点特征 (Point-level Features)
针对每个测温点 i 在时刻 t 计算的特征：

```python
class PointLevelFeatures:
    # 基础温度特征
    T_current: float                # T_i(t) - 当前绝对温度
    T_max_24h: float               # max(T_i(t-24h:t)) - 24小时最高温
    T_min_24h: float               # min(T_i(t-24h:t)) - 24小时最低温
    T_mean_7d: float               # mean(T_i(t-7d:t)) - 7天平均温度

    # 温升速率特征
    rate_1h: float                 # T_i(t) - T_i(t-1h) - 1小时温升
    rate_6h: float                 # T_i(t) - T_i(t-6h) - 6小时温升
    rate_24h: float                # T_i(t) - T_i(t-24h) - 24小时温升
    rate_7d: float                 # T_i(t) - T_i(t-7d) - 7天温升

    # 空间关系特征
    neighbor_deviation: float       # T_i(t) - mean(T_neighbors(t)) - 与邻域偏差
    layer_deviation: float          # T_i(t) - mean(T_layer(t)) - 与同层偏差
    region_deviation: float         # T_i(t) - mean(T_region(t)) - 与区域偏差

    # 稳定性特征
    volatility_24h: float          # std(T_i(t-24h:t)) - 24小时波动性
    volatility_7d: float           # std(T_i(t-7d:t)) - 7天波动性
    trend_consistency: float        # 温升趋势一致性指标

    # 异常检测特征
    is_local_hotspot: bool         # 是否为局部热点
    hotspot_intensity: float       # 热点强度（相对邻域的温度偏差）
    anomaly_score: float           # 基于历史模式的异常评分
```

### 3.1.1.2 聚合特征 (Aggregate-level Features)
按区域（层、圈、垂直区域、全仓）进行聚合计算：

```python
class AggregateLevelFeatures:
    # 区域定义
    REGIONS = {
        'surface': 'Z坐标 > 粮堆高度的80%',      # 表层
        'middle': '20% < Z坐标 < 80%',           # 中部
        'bottom': 'Z坐标 < 20%',                 # 底部
        'outer_ring': '径向坐标 > 仓储半径的70%', # 外圈
        'inner_ring': '径向坐标 < 30%',          # 内圈
        'whole_silo': '全仓范围'                 # 整仓
    }

    # 基础统计特征
    region_T_max: float            # max(T_region(t)) - 区域最高温
    region_T_min: float            # min(T_region(t)) - 区域最低温
    region_T_mean: float           # mean(T_region(t)) - 区域平均温
    region_T_median: float         # median(T_region(t)) - 区域中位温度
    region_T_std: float            # std(T_region(t)) - 区域温度标准差
    region_T_range: float          # max - min - 区域温度范围

    # 分布特征
    region_high_temp_ratio: float  # count(T_i > 阈值) / count(T_i) - 高温点占比
    region_hotspot_count: int      # 区域内热点数量
    region_hotspot_area: float     # 热点面积占区域面积比例

    # 变化特征
    region_rate_mean: float        # mean(rate_24h) - 区域平均温升速率
    region_rate_max: float         # max(rate_24h) - 区域最大温升速率
    region_rate_std: float         # std(rate_24h) - 温升速率标准差

    # 均匀性特征
    temperature_uniformity: float   # 温度分布均匀性指数
    spatial_autocorr: float        # 空间自相关系数
    cluster_coefficient: float     # 聚类系数
```

### 3.1.1.3 空间梯度特征 (Spatial-Gradient Features)
计算不同区域间的温差关系：

```python
class SpatialGradientFeatures:
    # 垂直梯度特征
    vertical_gradient: float       # Mean(T_surface) - Mean(T_bottom) - 垂直温差
    surface_middle_diff: float     # Mean(T_surface) - Mean(T_middle) - 表中温差
    middle_bottom_diff: float      # Mean(T_middle) - Mean(T_bottom) - 中底温差
    max_layer_diff: float          # max(Mean(T_layer)) - min(Mean(T_layer)) - 最大层间温差

    # 径向梯度特征
    radial_gradient: float         # Mean(T_outer) - Mean(T_inner) - 径向温差
    center_edge_ratio: float       # Mean(T_center) / Mean(T_edge) - 中心边缘比

    # 方向性特征
    north_south_diff: float        # 南北方向温差
    east_west_diff: float          # 东西方向温差
    max_directional_diff: float    # 最大方向性温差

    # 梯度变化特征
    gradient_change_24h: float     # 24小时梯度变化
    gradient_stability: float     # 梯度稳定性指标
```

### 3.1.1.4 时序特征 (Temporal Features)
基于时间序列分析的特征：

```python
class TemporalFeatures:
    # 趋势特征
    linear_trend_slope: float      # 线性趋势斜率
    trend_strength: float          # 趋势强度
    trend_direction: int           # 趋势方向（上升/下降/平稳）

    # 周期性特征
    daily_pattern_strength: float  # 日周期模式强度
    weekly_pattern_strength: float # 周周期模式强度
    seasonal_component: float      # 季节性成分

    # 突变检测特征
    change_point_count: int        # 突变点数量
    max_change_magnitude: float    # 最大突变幅度
    change_point_recency: float    # 最近突变点距离

    # 自相关特征
    autocorr_lag1: float          # 1阶自相关
    autocorr_lag24: float         # 24小时滞后自相关
    autocorr_lag168: float        # 7天滞后自相关

    # 复杂性特征
    entropy: float                # 时序熵
    fractal_dimension: float      # 分形维数
    hurst_exponent: float         # Hurst指数
```

### ******* 多源数据融合特征 (Multi-source Features)
整合环境、设备、操作等多源数据：

```python
class MultiSourceFeatures:
    # 环境关联特征
    indoor_outdoor_temp_corr: float    # 室内外温度相关性
    weather_impact_score: float       # 天气影响评分
    seasonal_deviation: float         # 季节性偏差
    humidity_temp_relationship: float # 湿度温度关系

    # 设备状态特征
    ventilation_efficiency: float     # 通风效率
    cooling_performance: float        # 制冷性能
    equipment_health_score: float     # 设备健康评分
    maintenance_overdue_days: int     # 维护超期天数

    # 操作质量特征
    operation_timeliness: float       # 操作及时性
    response_delay: float             # 响应延迟
    standard_compliance: float        # 标准符合度
    operator_experience: float        # 操作员经验水平

    # 数据质量特征
    data_completeness: float          # 数据完整性
    sensor_reliability: float        # 传感器可靠性
    measurement_accuracy: float       # 测量精度
    data_consistency: float          # 数据一致性
```

### ******* 特征计算的具体实现

#### 特征计算时间窗口和采样策略
```python
class FeatureCalculationConfig:
    # 时间窗口配置
    TIME_WINDOWS = {
        'real_time': 0,        # 实时数据
        'short_term': 24,      # 24小时短期
        'medium_term': 168,    # 7天中期
        'long_term': 720       # 30天长期
    }

    # 采样频率配置
    SAMPLING_FREQUENCY = {
        'temperature': 4,      # 温度数据每4小时采样一次
        'environment': 1,      # 环境数据每小时采样一次
        'equipment': 24,       # 设备状态每天采样一次
        'operation': 24        # 操作记录每天采样一次
    }

    # 数据预处理参数
    PREPROCESSING_PARAMS = {
        'outlier_threshold': 3.0,    # 异常值检测阈值（3σ原则）
        'missing_tolerance': 0.1,    # 缺失数据容忍度（10%）
        'interpolation_method': 'linear',  # 插值方法
        'smoothing_window': 3        # 平滑窗口大小
    }
```

#### 具体特征计算公式和算法
```python
class FeatureCalculationAlgorithms:

    @staticmethod
    def calculate_point_features(temp_series: np.array, timestamps: np.array,
                               neighbors: List[np.array]) -> dict:
        """计算单点特征的具体算法"""
        features = {}

        # 基础温度特征
        features['T_current'] = temp_series[-1]
        features['T_max_24h'] = np.max(temp_series[-24:])
        features['T_min_24h'] = np.min(temp_series[-24:])
        features['T_mean_7d'] = np.mean(temp_series[-168:])

        # 温升速率特征（具体计算公式）
        features['rate_1h'] = temp_series[-1] - temp_series[-1] if len(temp_series) > 1 else 0
        features['rate_6h'] = temp_series[-1] - temp_series[-6] if len(temp_series) > 6 else 0
        features['rate_24h'] = temp_series[-1] - temp_series[-24] if len(temp_series) > 24 else 0
        features['rate_7d'] = temp_series[-1] - temp_series[-168] if len(temp_series) > 168 else 0

        # 空间关系特征（邻域分析）
        if neighbors:
            neighbor_temps = [neighbor[-1] for neighbor in neighbors]  # 邻域当前温度
            features['neighbor_deviation'] = temp_series[-1] - np.mean(neighbor_temps)
            features['neighbor_max_diff'] = temp_series[-1] - np.max(neighbor_temps)
            features['neighbor_min_diff'] = temp_series[-1] - np.min(neighbor_temps)

        # 稳定性特征（统计分析）
        features['volatility_24h'] = np.std(temp_series[-24:]) if len(temp_series) > 24 else 0
        features['volatility_7d'] = np.std(temp_series[-168:]) if len(temp_series) > 168 else 0

        # 趋势一致性（线性回归分析）
        if len(temp_series) > 24:
            x = np.arange(len(temp_series[-24:]))
            y = temp_series[-24:]
            slope, _, r_value, _, _ = stats.linregress(x, y)
            features['trend_consistency'] = r_value ** 2  # R²值表示趋势一致性
            features['trend_slope'] = slope

        return features

    @staticmethod
    def calculate_aggregate_features(region_temps: np.array, region_coords: np.array) -> dict:
        """计算聚合特征的具体算法"""
        features = {}

        # 基础统计特征
        features['region_T_max'] = np.max(region_temps)
        features['region_T_min'] = np.min(region_temps)
        features['region_T_mean'] = np.mean(region_temps)
        features['region_T_median'] = np.median(region_temps)
        features['region_T_std'] = np.std(region_temps)
        features['region_T_range'] = features['region_T_max'] - features['region_T_min']

        # 分布特征（基于阈值分析）
        temp_threshold = features['region_T_mean'] + 2 * features['region_T_std']
        features['region_high_temp_ratio'] = np.sum(region_temps > temp_threshold) / len(region_temps)

        # 热点检测（基于聚类分析）
        hotspots = FeatureCalculationAlgorithms._detect_hotspots(region_temps, region_coords)
        features['region_hotspot_count'] = len(hotspots)
        features['region_hotspot_area'] = sum([hotspot['area'] for hotspot in hotspots])

        # 均匀性特征（基于空间统计）
        features['temperature_uniformity'] = 1.0 / (1.0 + features['region_T_std'])  # 均匀性指数
        features['spatial_autocorr'] = FeatureCalculationAlgorithms._calculate_spatial_autocorr(
            region_temps, region_coords)

        return features

    @staticmethod
    def _detect_hotspots(temps: np.array, coords: np.array, threshold_factor: float = 2.0) -> List[dict]:
        """热点检测算法（基于DBSCAN聚类）"""
        from sklearn.cluster import DBSCAN

        # 识别高温点
        temp_threshold = np.mean(temps) + threshold_factor * np.std(temps)
        high_temp_indices = np.where(temps > temp_threshold)[0]

        if len(high_temp_indices) < 2:
            return []

        # 对高温点进行空间聚类
        high_temp_coords = coords[high_temp_indices]
        clustering = DBSCAN(eps=1.0, min_samples=2).fit(high_temp_coords)

        hotspots = []
        for cluster_id in set(clustering.labels_):
            if cluster_id == -1:  # 噪声点
                continue

            cluster_indices = high_temp_indices[clustering.labels_ == cluster_id]
            cluster_temps = temps[cluster_indices]
            cluster_coords = coords[cluster_indices]

            hotspot = {
                'center': np.mean(cluster_coords, axis=0),
                'max_temp': np.max(cluster_temps),
                'mean_temp': np.mean(cluster_temps),
                'area': len(cluster_indices),  # 简化的面积计算
                'intensity': np.max(cluster_temps) - np.mean(temps)
            }
            hotspots.append(hotspot)

        return hotspots

    @staticmethod
    def _calculate_spatial_autocorr(temps: np.array, coords: np.array) -> float:
        """空间自相关计算（Moran's I指数）"""
        from scipy.spatial.distance import pdist, squareform

        # 计算距离矩阵
        distances = squareform(pdist(coords))

        # 构建空间权重矩阵（距离倒数权重）
        weights = 1.0 / (distances + 1e-6)  # 避免除零
        np.fill_diagonal(weights, 0)  # 对角线设为0

        # 计算Moran's I指数
        n = len(temps)
        temp_mean = np.mean(temps)
        temp_dev = temps - temp_mean

        numerator = np.sum(weights * np.outer(temp_dev, temp_dev))
        denominator = np.sum(weights) * np.sum(temp_dev ** 2)

        morans_i = (n / denominator) * numerator if denominator > 0 else 0
        return morans_i
```

### ******* 特征向量构建和存储
```python
class FeatureVectorBuilder:
    """特征向量构建器"""

    def __init__(self):
        self.feature_names = []
        self.feature_dimensions = {}
        self._initialize_feature_schema()

    def _initialize_feature_schema(self):
        """初始化特征模式"""
        self.feature_schema = {
            'point_features': {
                'basic_temp': ['T_current', 'T_max_24h', 'T_min_24h', 'T_mean_7d'],
                'rate_features': ['rate_1h', 'rate_6h', 'rate_24h', 'rate_7d'],
                'spatial_features': ['neighbor_deviation', 'neighbor_max_diff', 'neighbor_min_diff'],
                'stability_features': ['volatility_24h', 'volatility_7d', 'trend_consistency']
            },
            'aggregate_features': {
                'statistical': ['region_T_max', 'region_T_min', 'region_T_mean', 'region_T_std'],
                'distribution': ['region_high_temp_ratio', 'region_hotspot_count'],
                'uniformity': ['temperature_uniformity', 'spatial_autocorr']
            },
            'gradient_features': {
                'vertical': ['vertical_gradient', 'surface_middle_diff', 'middle_bottom_diff'],
                'radial': ['radial_gradient', 'center_edge_ratio'],
                'directional': ['north_south_diff', 'east_west_diff']
            },
            'temporal_features': {
                'trend': ['linear_trend_slope', 'trend_strength'],
                'periodicity': ['daily_pattern_strength', 'weekly_pattern_strength'],
                'complexity': ['entropy', 'fractal_dimension']
            }
        }

        # 计算总特征维度
        total_dims = 0
        for category in self.feature_schema.values():
            for subcategory, features in category.items():
                total_dims += len(features)

        self.total_feature_dimensions = total_dims
        print(f"总特征维度: {total_dims}")

    def build_feature_vector(self, point_features: dict, aggregate_features: dict,
                           gradient_features: dict, temporal_features: dict) -> np.array:
        """构建完整的特征向量"""
        feature_vector = []

        # 按照预定义顺序添加特征
        for category_name, category in self.feature_schema.items():
            if category_name == 'point_features':
                source_features = point_features
            elif category_name == 'aggregate_features':
                source_features = aggregate_features
            elif category_name == 'gradient_features':
                source_features = gradient_features
            elif category_name == 'temporal_features':
                source_features = temporal_features

            for subcategory, feature_names in category.items():
                for feature_name in feature_names:
                    value = source_features.get(feature_name, 0.0)  # 默认值为0
                    feature_vector.append(value)

        return np.array(feature_vector)

    def save_feature_vector(self, feature_vector: np.array, timestamp: str,
                          silo_id: str, storage_path: str):
        """保存特征向量到存储系统"""
        feature_data = {
            'timestamp': timestamp,
            'silo_id': silo_id,
            'feature_vector': feature_vector.tolist(),
            'feature_dimensions': self.total_feature_dimensions,
            'feature_schema': self.feature_schema
        }

        # 保存为JSON格式（实际应用中可能使用数据库或HDF5）
        import json
        filename = f"{storage_path}/features_{silo_id}_{timestamp}.json"
        with open(filename, 'w') as f:
            json.dump(feature_data, f, indent=2)
```

#### 3.1.2 模块二：AI异常检测模型（重构）
**检测目标**：14类异常现象
**模型架构**：
```
多任务学习框架：
├─ 共享特征提取层
├─ 空间局部性异常检测分支（4类）
│  ├─ 表层局部发热
│  ├─ 中部局部发热
│  ├─ 底部局部发热
│  └─ 垂直局部发热
├─ 空间整体性异常检测分支（4类）
│  ├─ 表层整体发热
│  ├─ 中部整体发热
│  ├─ 底部整体发热
│  └─ 垂直整体发热
├─ 温度阈值异常检测分支（2类）
│  ├─ 超最高温异常
│  └─ 温升速率异常
└─ 分布均匀性异常检测分支（4类）
   ├─ 水平分布不均
   ├─ 外圈温度偏高
   ├─ 全局温差过大
   └─ 整仓温度偏高
```

**详细技术实现方案**：

### 3.1.2.1 多任务学习框架详细设计
```python
import torch
import torch.nn as nn
import torch.nn.functional as F
from typing import Dict, List, Tuple

class MultiTaskAnomalyDetectionModel(nn.Module):
    """多任务异常检测模型"""

    def __init__(self, input_dim: int, hidden_dims: List[int] = [512, 256, 128]):
        super().__init__()

        # 共享特征提取层
        self.shared_layers = nn.ModuleList()
        prev_dim = input_dim

        for hidden_dim in hidden_dims:
            self.shared_layers.append(nn.Linear(prev_dim, hidden_dim))
            self.shared_layers.append(nn.BatchNorm1d(hidden_dim))
            self.shared_layers.append(nn.ReLU())
            self.shared_layers.append(nn.Dropout(0.3))
            prev_dim = hidden_dim

        # 任务特定分支
        self.spatial_local_branch = self._create_task_branch(prev_dim, 4)      # 空间局部性异常
        self.spatial_global_branch = self._create_task_branch(prev_dim, 4)     # 空间整体性异常
        self.threshold_branch = self._create_task_branch(prev_dim, 2)          # 温度阈值异常
        self.uniformity_branch = self._create_task_branch(prev_dim, 4)         # 分布均匀性异常

        # 注意力机制（用于特征重要性学习）
        self.attention = nn.MultiheadAttention(prev_dim, num_heads=8)

    def _create_task_branch(self, input_dim: int, num_classes: int) -> nn.Module:
        """创建任务特定分支"""
        return nn.Sequential(
            nn.Linear(input_dim, 64),
            nn.ReLU(),
            nn.Dropout(0.2),
            nn.Linear(64, 32),
            nn.ReLU(),
            nn.Linear(32, num_classes),
            nn.Sigmoid()  # 多标签分类使用Sigmoid
        )

    def forward(self, x: torch.Tensor) -> Dict[str, torch.Tensor]:
        """前向传播"""
        # 共享特征提取
        shared_features = x
        for layer in self.shared_layers:
            shared_features = layer(shared_features)

        # 注意力机制增强特征
        attended_features, _ = self.attention(
            shared_features.unsqueeze(0),
            shared_features.unsqueeze(0),
            shared_features.unsqueeze(0)
        )
        attended_features = attended_features.squeeze(0)

        # 各分支预测
        outputs = {
            'spatial_local': self.spatial_local_branch(attended_features),
            'spatial_global': self.spatial_global_branch(attended_features),
            'threshold': self.threshold_branch(attended_features),
            'uniformity': self.uniformity_branch(attended_features)
        }

        return outputs

class AnomalyDetectionTrainer:
    """异常检测模型训练器"""

    def __init__(self, model: MultiTaskAnomalyDetectionModel, device: str = 'cuda'):
        self.model = model.to(device)
        self.device = device

        # 损失函数（针对不同任务使用不同权重）
        self.criterion = nn.BCELoss()
        self.task_weights = {
            'spatial_local': 1.0,
            'spatial_global': 1.0,
            'threshold': 2.0,      # 阈值异常权重更高（安全关键）
            'uniformity': 0.8
        }

        # 优化器
        self.optimizer = torch.optim.AdamW(
            model.parameters(),
            lr=0.001,
            weight_decay=0.01
        )

        # 学习率调度器
        self.scheduler = torch.optim.lr_scheduler.CosineAnnealingLR(
            self.optimizer, T_max=100
        )

    def train_step(self, batch_data: Dict[str, torch.Tensor]) -> Dict[str, float]:
        """单步训练"""
        self.model.train()

        features = batch_data['features'].to(self.device)
        labels = {
            task: batch_data[f'{task}_labels'].to(self.device)
            for task in self.task_weights.keys()
        }

        # 前向传播
        outputs = self.model(features)

        # 计算损失
        total_loss = 0
        task_losses = {}

        for task, weight in self.task_weights.items():
            task_loss = self.criterion(outputs[task], labels[task])
            task_losses[task] = task_loss.item()
            total_loss += weight * task_loss

        # 反向传播
        self.optimizer.zero_grad()
        total_loss.backward()

        # 梯度裁剪
        torch.nn.utils.clip_grad_norm_(self.model.parameters(), max_norm=1.0)

        self.optimizer.step()

        task_losses['total'] = total_loss.item()
        return task_losses
```

### ******* 异常类型与模型输出的映射关系
```python
class AnomalyTypeMapper:
    """异常类型映射器"""

    def __init__(self):
        # 定义异常类型到模型输出的映射
        self.anomaly_mapping = {
            # 空间局部性异常（4类）
            'spatial_local': {
                0: 'surface_local_heating',      # 表层局部发热
                1: 'middle_local_heating',       # 中部局部发热
                2: 'bottom_local_heating',       # 底部局部发热
                3: 'vertical_local_heating'      # 垂直局部发热
            },
            # 空间整体性异常（4类）
            'spatial_global': {
                0: 'surface_global_heating',     # 表层整体发热
                1: 'middle_global_heating',      # 中部整体发热
                2: 'bottom_global_heating',      # 底部整体发热
                3: 'vertical_global_heating'     # 垂直整体发热
            },
            # 温度阈值异常（2类）
            'threshold': {
                0: 'max_temperature_exceeded',   # 超最高温异常
                1: 'temperature_rise_rate_anomaly'  # 温升速率异常
            },
            # 分布均匀性异常（4类）
            'uniformity': {
                0: 'horizontal_distribution_unevenness',  # 水平分布不均
                1: 'outer_ring_temperature_deviation',    # 外圈温度偏高
                2: 'global_temperature_range_excessive',  # 全局温差过大
                3: 'whole_silo_temperature_elevation'     # 整仓温度偏高
            }
        }

        # 反向映射：异常类型到分支和索引
        self.reverse_mapping = {}
        for branch, anomalies in self.anomaly_mapping.items():
            for idx, anomaly_type in anomalies.items():
                self.reverse_mapping[anomaly_type] = (branch, idx)

    def decode_predictions(self, model_outputs: Dict[str, torch.Tensor],
                          threshold: float = 0.5) -> List[Dict]:
        """解码模型预测结果"""
        detected_anomalies = []

        for branch, outputs in model_outputs.items():
            # 获取超过阈值的预测
            predictions = (outputs > threshold).cpu().numpy()
            confidences = outputs.cpu().numpy()

            for idx, (is_anomaly, confidence) in enumerate(zip(predictions, confidences)):
                if is_anomaly:
                    anomaly_type = self.anomaly_mapping[branch][idx]
                    detected_anomalies.append({
                        'anomaly_type': anomaly_type,
                        'branch': branch,
                        'confidence': float(confidence),
                        'severity': self._calculate_severity(confidence),
                        'priority': self._calculate_priority(anomaly_type, confidence)
                    })

        # 按优先级排序
        detected_anomalies.sort(key=lambda x: x['priority'], reverse=True)
        return detected_anomalies

    def _calculate_severity(self, confidence: float) -> str:
        """计算异常严重程度"""
        if confidence >= 0.9:
            return 'critical'
        elif confidence >= 0.7:
            return 'high'
        elif confidence >= 0.5:
            return 'medium'
        else:
            return 'low'

    def _calculate_priority(self, anomaly_type: str, confidence: float) -> float:
        """计算异常处理优先级"""
        # 基础优先级权重
        base_weights = {
            'max_temperature_exceeded': 1.0,           # 最高优先级
            'whole_silo_temperature_elevation': 0.95,  # 整仓问题
            'temperature_rise_rate_anomaly': 0.9,      # 温升异常
            'surface_local_heating': 0.8,              # 表层局部
            'middle_local_heating': 0.85,              # 中部局部
            'bottom_local_heating': 0.75,              # 底部局部
            'surface_global_heating': 0.7,             # 表层整体
            'middle_global_heating': 0.8,              # 中部整体
            'bottom_global_heating': 0.75,             # 底部整体
            'vertical_local_heating': 0.7,             # 垂直局部
            'vertical_global_heating': 0.75,           # 垂直整体
            'horizontal_distribution_unevenness': 0.6, # 水平分布
            'outer_ring_temperature_deviation': 0.65,  # 外圈偏高
            'global_temperature_range_excessive': 0.7  # 全局温差
        }

        base_weight = base_weights.get(anomaly_type, 0.5)
        return base_weight * confidence
```

### 3.1.2.3 训练数据标注和样本平衡策略
```python
class DataAnnotationStrategy:
    """数据标注策略"""

    def __init__(self):
        # 异常检测阈值配置
        self.detection_thresholds = {
            # 空间局部性异常阈值
            'local_heating': {
                'neighbor_deviation_threshold': 3.0,    # 邻域温差阈值
                'hotspot_intensity_threshold': 2.0,     # 热点强度阈值
                'spatial_cluster_min_size': 3           # 最小聚类大小
            },
            # 空间整体性异常阈值
            'global_heating': {
                'region_temp_threshold': 25.0,          # 区域温度阈值
                'region_std_threshold': 1.5,            # 区域标准差阈值
                'coverage_ratio_threshold': 0.7         # 覆盖率阈值
            },
            # 温度阈值异常
            'threshold_anomaly': {
                'max_temp_threshold': 30.0,             # 最高温度阈值
                'rise_rate_1h_threshold': 2.0,          # 1小时温升阈值
                'rise_rate_24h_threshold': 5.0,         # 24小时温升阈值
                'rise_rate_7d_threshold': 10.0          # 7天温升阈值
            },
            # 分布均匀性异常阈值
            'uniformity_anomaly': {
                'horizontal_std_threshold': 2.0,        # 水平标准差阈值
                'radial_gradient_threshold': 3.0,       # 径向梯度阈值
                'global_range_threshold': 8.0,          # 全局范围阈值
                'uniformity_index_threshold': 0.7       # 均匀性指数阈值
            }
        }

    def auto_annotate_sample(self, features: Dict[str, float]) -> Dict[str, List[int]]:
        """自动标注样本"""
        labels = {
            'spatial_local': [0, 0, 0, 0],      # 4类空间局部性异常
            'spatial_global': [0, 0, 0, 0],     # 4类空间整体性异常
            'threshold': [0, 0],                # 2类温度阈值异常
            'uniformity': [0, 0, 0, 0]          # 4类分布均匀性异常
        }

        # 空间局部性异常标注
        if features.get('neighbor_deviation', 0) > self.detection_thresholds['local_heating']['neighbor_deviation_threshold']:
            # 根据位置信息确定具体类型
            if features.get('surface_indicator', 0) > 0.5:
                labels['spatial_local'][0] = 1  # 表层局部发热
            elif features.get('middle_indicator', 0) > 0.5:
                labels['spatial_local'][1] = 1  # 中部局部发热
            elif features.get('bottom_indicator', 0) > 0.5:
                labels['spatial_local'][2] = 1  # 底部局部发热

        # 空间整体性异常标注
        if features.get('region_T_mean', 0) > self.detection_thresholds['global_heating']['region_temp_threshold']:
            # 根据区域信息确定具体类型
            if features.get('surface_coverage', 0) > self.detection_thresholds['global_heating']['coverage_ratio_threshold']:
                labels['spatial_global'][0] = 1  # 表层整体发热
            # ... 其他类型的判定逻辑

        # 温度阈值异常标注
        if features.get('T_current', 0) > self.detection_thresholds['threshold_anomaly']['max_temp_threshold']:
            labels['threshold'][0] = 1  # 超最高温异常

        if features.get('rate_24h', 0) > self.detection_thresholds['threshold_anomaly']['rise_rate_24h_threshold']:
            labels['threshold'][1] = 1  # 温升速率异常

        # 分布均匀性异常标注
        if features.get('region_T_std', 0) > self.detection_thresholds['uniformity_anomaly']['horizontal_std_threshold']:
            labels['uniformity'][0] = 1  # 水平分布不均

        # ... 其他均匀性异常的判定逻辑

        return labels

class SampleBalancingStrategy:
    """样本平衡策略"""

    def __init__(self):
        # 各类异常的期望比例
        self.target_ratios = {
            'spatial_local': 0.15,      # 空间局部性异常占15%
            'spatial_global': 0.10,     # 空间整体性异常占10%
            'threshold': 0.05,          # 温度阈值异常占5%（较少但重要）
            'uniformity': 0.20,         # 分布均匀性异常占20%
            'normal': 0.50              # 正常样本占50%
        }

    def balance_dataset(self, dataset: List[Dict]) -> List[Dict]:
        """平衡数据集"""
        from collections import defaultdict
        import random

        # 按类别分组样本
        samples_by_category = defaultdict(list)

        for sample in dataset:
            # 确定样本的主要类别
            main_category = self._get_main_category(sample['labels'])
            samples_by_category[main_category].append(sample)

        # 计算目标样本数量
        total_samples = len(dataset)
        target_counts = {
            category: int(total_samples * ratio)
            for category, ratio in self.target_ratios.items()
        }

        # 平衡各类别样本
        balanced_dataset = []

        for category, target_count in target_counts.items():
            category_samples = samples_by_category[category]

            if len(category_samples) >= target_count:
                # 随机采样到目标数量
                balanced_samples = random.sample(category_samples, target_count)
            else:
                # 过采样到目标数量
                balanced_samples = self._oversample(category_samples, target_count)

            balanced_dataset.extend(balanced_samples)

        # 随机打乱
        random.shuffle(balanced_dataset)
        return balanced_dataset

    def _get_main_category(self, labels: Dict[str, List[int]]) -> str:
        """获取样本的主要类别"""
        # 检查是否有任何异常
        for category, label_list in labels.items():
            if any(label_list):
                return category
        return 'normal'

    def _oversample(self, samples: List[Dict], target_count: int) -> List[Dict]:
        """过采样策略"""
        if not samples:
            return []

        # 使用SMOTE类似的策略生成合成样本
        oversampled = samples.copy()

        while len(oversampled) < target_count:
            # 随机选择两个样本进行插值
            sample1, sample2 = random.sample(samples, 2)
            synthetic_sample = self._create_synthetic_sample(sample1, sample2)
            oversampled.append(synthetic_sample)

        return oversampled[:target_count]

    def _create_synthetic_sample(self, sample1: Dict, sample2: Dict) -> Dict:
        """创建合成样本"""
        # 简化的线性插值策略
        alpha = random.random()

        synthetic_features = {}
        for key in sample1['features']:
            if isinstance(sample1['features'][key], (int, float)):
                synthetic_features[key] = (
                    alpha * sample1['features'][key] +
                    (1 - alpha) * sample2['features'][key]
                )
            else:
                synthetic_features[key] = sample1['features'][key]  # 非数值特征保持不变

        return {
            'features': synthetic_features,
            'labels': sample1['labels']  # 标签保持与sample1一致
        }
```

### ******* 特征到异常检测的完整数据流
```python
class AnomalyDetectionPipeline:
    """异常检测完整流水线"""

    def __init__(self, model_path: str, device: str = 'cuda'):
        # 加载训练好的模型
        self.model = torch.load(model_path, map_location=device)
        self.model.eval()
        self.device = device

        # 初始化组件
        self.feature_builder = FeatureVectorBuilder()
        self.anomaly_mapper = AnomalyTypeMapper()

        # 特征重要性权重（从训练中学习得到）
        self.feature_importance_weights = self._load_feature_importance()

        # 动态阈值配置
        self.dynamic_thresholds = {
            'spatial_local': 0.6,
            'spatial_global': 0.5,
            'threshold': 0.8,      # 阈值异常使用更高的置信度要求
            'uniformity': 0.5
        }

    def detect_anomalies(self, raw_data: Dict) -> Dict:
        """完整的异常检测流程"""

        # 步骤1: 数据预处理和特征计算
        processed_features = self._preprocess_and_extract_features(raw_data)

        # 步骤2: 构建特征向量
        feature_vector = self._build_feature_vector(processed_features)

        # 步骤3: 模型推理
        model_outputs = self._model_inference(feature_vector)

        # 步骤4: 后处理和结果解析
        detection_results = self._postprocess_results(model_outputs, processed_features)

        # 步骤5: 多异常冲突处理
        final_results = self._handle_multiple_anomalies(detection_results)

        return final_results

    def _preprocess_and_extract_features(self, raw_data: Dict) -> Dict:
        """数据预处理和特征提取"""

        # 原始数据包含：温度时序数据、空间坐标、环境数据等
        temp_data = raw_data['temperature_data']      # 形状: (n_sensors, n_timesteps)
        coordinates = raw_data['sensor_coordinates']  # 形状: (n_sensors, 3)
        timestamps = raw_data['timestamps']           # 形状: (n_timesteps,)
        env_data = raw_data.get('environment_data', {})

        # 数据质量检查和清洗
        temp_data_cleaned = self._clean_temperature_data(temp_data)

        # 计算各类特征
        features = {}

        # 单点特征计算
        point_features = []
        for sensor_idx in range(len(temp_data_cleaned)):
            sensor_temp_series = temp_data_cleaned[sensor_idx]
            sensor_coords = coordinates[sensor_idx]

            # 获取邻域传感器数据
            neighbors = self._get_neighbor_sensors(sensor_idx, coordinates, temp_data_cleaned)

            # 计算单点特征
            point_feat = FeatureCalculationAlgorithms.calculate_point_features(
                sensor_temp_series, timestamps, neighbors
            )
            point_feat['sensor_id'] = sensor_idx
            point_feat['coordinates'] = sensor_coords
            point_features.append(point_feat)

        features['point_features'] = point_features

        # 聚合特征计算（按区域）
        regions = self._define_regions(coordinates)
        aggregate_features = {}

        for region_name, sensor_indices in regions.items():
            region_temps = temp_data_cleaned[sensor_indices, -1]  # 当前时刻温度
            region_coords = coordinates[sensor_indices]

            agg_feat = FeatureCalculationAlgorithms.calculate_aggregate_features(
                region_temps, region_coords
            )
            aggregate_features[region_name] = agg_feat

        features['aggregate_features'] = aggregate_features

        # 空间梯度特征计算
        gradient_features = self._calculate_gradient_features(
            temp_data_cleaned, coordinates, regions
        )
        features['gradient_features'] = gradient_features

        # 时序特征计算
        temporal_features = self._calculate_temporal_features(
            temp_data_cleaned, timestamps
        )
        features['temporal_features'] = temporal_features

        # 环境关联特征
        if env_data:
            env_features = self._calculate_environmental_features(
                temp_data_cleaned, env_data, timestamps
            )
            features['environmental_features'] = env_features

        return features

    def _build_feature_vector(self, processed_features: Dict) -> torch.Tensor:
        """构建模型输入的特征向量"""

        # 聚合所有特征到单一向量
        all_features = {}

        # 聚合单点特征（取统计量）
        point_features = processed_features['point_features']
        for feat_name in ['T_current', 'rate_24h', 'neighbor_deviation', 'volatility_7d']:
            values = [pf.get(feat_name, 0) for pf in point_features]
            all_features[f'{feat_name}_mean'] = np.mean(values)
            all_features[f'{feat_name}_max'] = np.max(values)
            all_features[f'{feat_name}_std'] = np.std(values)

        # 添加聚合特征
        for region_name, region_features in processed_features['aggregate_features'].items():
            for feat_name, feat_value in region_features.items():
                all_features[f'{region_name}_{feat_name}'] = feat_value

        # 添加梯度特征
        all_features.update(processed_features['gradient_features'])

        # 添加时序特征
        all_features.update(processed_features['temporal_features'])

        # 添加环境特征
        if 'environmental_features' in processed_features:
            all_features.update(processed_features['environmental_features'])

        # 构建特征向量（按预定义顺序）
        feature_vector = self.feature_builder.build_feature_vector(
            all_features, all_features, all_features, all_features
        )

        # 特征标准化
        feature_vector = self._normalize_features(feature_vector)

        return torch.tensor(feature_vector, dtype=torch.float32).unsqueeze(0).to(self.device)

    def _model_inference(self, feature_vector: torch.Tensor) -> Dict[str, torch.Tensor]:
        """模型推理"""
        with torch.no_grad():
            outputs = self.model(feature_vector)
        return outputs

    def _postprocess_results(self, model_outputs: Dict[str, torch.Tensor],
                           processed_features: Dict) -> List[Dict]:
        """后处理模型输出"""

        # 使用动态阈值进行异常检测
        detected_anomalies = []

        for branch, outputs in model_outputs.items():
            threshold = self.dynamic_thresholds[branch]

            # 应用特征重要性权重调整置信度
            adjusted_outputs = self._apply_feature_importance_weighting(
                outputs, branch, processed_features
            )

            # 检测超过阈值的异常
            predictions = (adjusted_outputs > threshold).cpu().numpy().flatten()
            confidences = adjusted_outputs.cpu().numpy().flatten()

            for idx, (is_anomaly, confidence) in enumerate(zip(predictions, confidences)):
                if is_anomaly:
                    anomaly_info = self.anomaly_mapper.anomaly_mapping[branch][idx]

                    detected_anomalies.append({
                        'anomaly_type': anomaly_info,
                        'branch': branch,
                        'confidence': float(confidence),
                        'raw_confidence': float(model_outputs[branch].cpu().numpy().flatten()[idx]),
                        'severity': self._calculate_severity(confidence),
                        'spatial_location': self._determine_spatial_location(anomaly_info, processed_features),
                        'affected_sensors': self._identify_affected_sensors(anomaly_info, processed_features),
                        'key_features': self._extract_key_features(anomaly_info, processed_features)
                    })

        return detected_anomalies

    def _handle_multiple_anomalies(self, detection_results: List[Dict]) -> Dict:
        """处理多个异常同时出现的情况"""

        if not detection_results:
            return {
                'status': 'normal',
                'anomalies': [],
                'summary': '未检测到异常',
                'recommendations': ['继续正常监控']
            }

        # 按优先级排序
        sorted_anomalies = sorted(
            detection_results,
            key=lambda x: self.anomaly_mapper._calculate_priority(x['anomaly_type'], x['confidence']),
            reverse=True
        )

        # 异常冲突检测和解决
        resolved_anomalies = self._resolve_anomaly_conflicts(sorted_anomalies)

        # 生成综合评估
        overall_severity = self._assess_overall_severity(resolved_anomalies)

        # 生成摘要
        summary = self._generate_anomaly_summary(resolved_anomalies)

        return {
            'status': 'anomaly_detected',
            'anomalies': resolved_anomalies,
            'overall_severity': overall_severity,
            'summary': summary,
            'detection_timestamp': datetime.now().isoformat(),
            'total_anomaly_count': len(resolved_anomalies)
        }

    def _resolve_anomaly_conflicts(self, anomalies: List[Dict]) -> List[Dict]:
        """解决异常冲突"""

        # 冲突规则定义
        conflict_rules = {
            # 如果检测到整仓温度偏高，则忽略局部发热异常
            'whole_silo_temperature_elevation': [
                'surface_local_heating', 'middle_local_heating', 'bottom_local_heating'
            ],
            # 如果检测到超最高温，则其他温度异常的优先级降低
            'max_temperature_exceeded': [
                'surface_global_heating', 'middle_global_heating', 'bottom_global_heating'
            ]
        }

        resolved = []
        suppressed_types = set()

        for anomaly in anomalies:
            anomaly_type = anomaly['anomaly_type']

            # 检查是否被抑制
            if anomaly_type in suppressed_types:
                anomaly['status'] = 'suppressed'
                anomaly['suppression_reason'] = '被更高优先级异常抑制'
                continue

            # 添加到结果中
            resolved.append(anomaly)

            # 检查是否需要抑制其他异常
            if anomaly_type in conflict_rules:
                suppressed_types.update(conflict_rules[anomaly_type])

        return resolved

    def _calculate_severity(self, confidence: float) -> str:
        """计算异常严重程度"""
        if confidence >= 0.9:
            return 'critical'
        elif confidence >= 0.7:
            return 'high'
        elif confidence >= 0.5:
            return 'medium'
        else:
            return 'low'

    def _assess_overall_severity(self, anomalies: List[Dict]) -> str:
        """评估整体严重程度"""
        if not anomalies:
            return 'normal'

        max_severity = max([
            {'critical': 4, 'high': 3, 'medium': 2, 'low': 1}[a['severity']]
            for a in anomalies
        ])

        severity_map = {4: 'critical', 3: 'high', 2: 'medium', 1: 'low'}
        return severity_map[max_severity]

    def _generate_anomaly_summary(self, anomalies: List[Dict]) -> str:
        """生成异常摘要"""
        if not anomalies:
            return "系统运行正常，未检测到异常。"

        summary_parts = []

        # 按严重程度分组
        by_severity = {}
        for anomaly in anomalies:
            severity = anomaly['severity']
            if severity not in by_severity:
                by_severity[severity] = []
            by_severity[severity].append(anomaly['anomaly_type'])

        # 生成摘要文本
        for severity in ['critical', 'high', 'medium', 'low']:
            if severity in by_severity:
                count = len(by_severity[severity])
                summary_parts.append(f"{severity}级异常{count}个")

        main_summary = f"检测到{len(anomalies)}个异常：" + "、".join(summary_parts)

        # 添加主要异常类型
        main_anomaly = anomalies[0]['anomaly_type']
        main_summary += f"。主要异常：{main_anomaly}"

        return main_summary
```

#### 3.1.3 模块三：根本原因推理引擎（新增）
**核心功能**：基于异常检测结果分析根本原因

**算法设计**：
```
1. 因果推理层
   - 贝叶斯网络建模
   - 因果图构建
   - 条件概率推理

2. 时序关联层
   - 滞后效应分析
   - 格兰杰因果检验
   - 时间序列因果发现

3. 证据融合层
   - 多源证据整合
   - 不确定性处理
   - 置信度评估

4. 知识推理层
   - 专家规则引擎
   - 案例推理
   - 模式匹配
```

**针对14种异常类型的算法策略**：

**空间分布异常（8类）**：
```
表层局部发热：
├─ 算法：局部异常检测 + 空间聚类 + 因果推理
├─ 特征：局部温度梯度、热点面积、邻域温差
├─ 重点原因：局部水分超标、虫害活动、密封问题
└─ 处理策略：局部检查、防止扩散

表层整体发热：
├─ 算法：整体统计分析 + 环境关联分析
├─ 特征：整体温度水平、环境相关性、时间模式
├─ 重点原因：环境因素、整体品质问题、通风不当
└─ 处理策略：整体通风、环境控制

中部局部发热：
├─ 算法：深度异常检测 + 传播模式分析
├─ 特征：内部温度分布、传播路径、持续时间
├─ 重点原因：内部品质变化、局部虫害
└─ 处理策略：深度检查、可能需要扒粮

中部整体发热：
├─ 算法：整体趋势分析 + 多因子关联
├─ 特征：整体温升趋势、多层温度关联
├─ 重点原因：整体品质问题、通风系统故障
└─ 处理策略：全面通风、大范围处理

底部局部发热：
├─ 算法：底部特征分析 + 地面因素关联
├─ 特征：底部温度分布、地面湿度、密封状态
├─ 重点原因：地面湿度、底部密封问题
└─ 处理策略：底部密封检查、底部通风

底部整体发热：
├─ 算法：底部系统分析 + 结构因素评估
├─ 特征：底部整体温度、结构完整性
├─ 重点原因：底部通风系统问题、结构问题
└─ 处理策略：底部系统全面检查

垂直局部发热：
├─ 算法：垂直梯度分析 + 通风效果评估
├─ 特征：垂直温度梯度、通风路径、局部阻塞
├─ 重点原因：垂直通风局部问题、局部阻塞
└─ 处理策略：垂直通风调整、局部疏通

垂直整体发热：
├─ 算法：垂直系统分析 + 整体通风评估
├─ 特征：整体垂直温度分布、通风系统状态
├─ 重点原因：垂直通风系统故障、整体品质问题
└─ 处理策略：垂直通风系统全面检查
```

**时间变化异常（2类）**：
```
温升速率异常：
├─ 算法：时序分析 + 变化率检测 + 滞后关联
├─ 特征：温升速率、加速度、时间模式
├─ 重点原因：品质变化、环境突变、操作不当、新粮后熟过程
└─ 处理策略：立即通风、密切监控、原因识别

超最高温：
├─ 算法：阈值检测 + 紧急评估 + 风险分析
├─ 特征：绝对温度值、超标程度、影响范围
├─ 重点原因：严重品质问题、设备故障、环境极端
└─ 处理策略：紧急降温、品质检测、风险评估
```

**分布均匀性异常（4类）**：
```
同层/圈不均：
├─ 算法：水平分布分析 + 通风效果评估
├─ 特征：同层温度方差、径向分布、通风均匀性
├─ 重点原因：水平通风不均、仓房结构问题
└─ 处理策略：调整水平通风策略、改善分布

外圈粮温过高：
├─ 算法：边缘效应分析 + 隔热性能评估
├─ 特征：边缘温度、内外温差、隔热效果
├─ 重点原因：隔热不良、密封问题、外界影响
└─ 处理策略：改善隔热、检查密封、边缘通风

仓内温差过大：
├─ 算法：全局分布分析 + 均匀性评估
├─ 特征：全局温度分布、温差统计、均匀性指标
├─ 重点原因：通风分布不均、仓房设计问题
└─ 处理策略：优化通风分布、改善均匀性

整仓发热：
├─ 算法：整体风险评估 + 紧急响应分析
├─ 特征：整体温度水平、发热强度、发展趋势
├─ 重点原因：整体品质严重问题、系统性故障
└─ 处理策略：全面应急处理、考虑出仓
```

**扩展的根本原因分析体系**：

基于14种异常现象，建立6大类根本原因的详细分析体系：

```
1. 环境因素分析
   ├─ 外界温湿度影响
   │  ├─ 特征：室内外温度相关性、滞后效应
   │  ├─ 算法：相关性分析、滞后关联分析
   │  └─ 判定：相关系数 > 0.7，滞后时间 < 6小时
   ├─ 气象条件变化
   │  ├─ 特征：风速、湿度、气压变化
   │  ├─ 算法：气象数据关联分析
   │  └─ 判定：气象变化与温度异常时间吻合
   ├─ 季节性因素
   │  ├─ 特征：季节模式匹配度、历史同期对比
   │  ├─ 算法：季节性分解、模式匹配
   │  └─ 判定：季节模式匹配度 > 0.8
   └─ 太阳辐射影响
      ├─ 特征：太阳辐射强度、方向性温差
      ├─ 算法：辐射强度关联分析
      └─ 判定：辐射强度 > 800W/m²，方向性温差明显

2. 粮食品质变化分析
   ├─ 水分含量超标
   │  ├─ 特征：水分检测值、持续温升天数
   │  ├─ 算法：水分阈值检测、温升模式分析
   │  └─ 判定：水分 > 14%，持续温升 > 7天
   ├─ 虫害活动
   │  ├─ 特征：局部热点聚集、CO2浓度
   │  ├─ 算法：热点聚类分析、生物活动指标
   │  └─ 判定：热点聚集度 > 0.7，CO2 > 1000ppm
   ├─ 霉菌生长
   │  ├─ 特征：湿度条件、温度适宜性
   │  ├─ 算法：霉菌生长条件评估
   │  └─ 判定：湿度 > 70%，温度20-30°C
   ├─ 呼吸作用增强
   │  ├─ 特征：CO2浓度变化、温升模式
   │  ├─ 算法：呼吸强度计算、代谢活动评估
   │  └─ 判定：CO2增长率 > 50ppm/天
   └─ 生物化学反应
      ├─ 特征：温度变化模式、化学指标
      ├─ 算法：反应动力学建模
      └─ 判定：指数型温升模式，反应速率常数异常

3. 设备故障分析
   ├─ 通风系统故障
   │  ├─ 特征：通风效率、气流分布
   │  ├─ 算法：通风效果评估、流场分析
   │  └─ 判定：通风效率 < 70%，分布不均
   ├─ 制冷系统故障
   │  ├─ 特征：制冷性能、温控效果
   │  ├─ 算法：制冷效率计算、温控分析
   │  └─ 判定：制冷效率 < 80%，温控失效
   ├─ 密封系统故障
   │  ├─ 特征：密封完整性、边缘温度
   │  ├─ 算法：密封性能评估、边缘效应分析
   │  └─ 判定：密封完整性 < 90%，边缘温度异常
   └─ 控制系统故障
      ├─ 特征：控制精度、响应时间
      ├─ 算法：控制系统性能分析
      └─ 判定：控制精度偏差 > 10%，响应延迟 > 30分钟
```

```
4. 操作管理因素分析
   ├─ 通风时机不当
   │  ├─ 特征：通风时机偏差、气象条件匹配
   │  ├─ 算法：时机优化分析、气象关联评估
   │  └─ 判定：时机偏差 > 4小时，气象条件不匹配
   ├─ 维护计划延误
   │  ├─ 特征：维护超期天数、设备性能下降
   │  ├─ 算法：维护计划符合度分析
   │  └─ 判定：维护超期 > 30天，性能下降 > 20%
   ├─ 响应速度缓慢
   │  ├─ 特征：异常发现到处理的时间间隔
   │  ├─ 算法：响应时间分析、处理效率评估
   │  └─ 判定：响应时间 > 24小时，处理延迟
   ├─ 操作标准偏差
   │  ├─ 特征：操作标准符合度、操作记录
   │  ├─ 算法：标准符合度评估、偏差分析
   │  └─ 判定：符合度 < 80%，偏差频繁
   └─ 人员经验不足
      ├─ 特征：操作员经验水平、培训完成率
      ├─ 算法：经验水平评估、能力分析
      └─ 判定：经验水平 < 60%，培训不足

5. 硬件故障分析（新增）
   ├─ 传感器点位故障
   │  ├─ 特征：单点数据异常、邻点对比、历史稳定性
   │  ├─ 算法：单点异常检测、传感器诊断
   │  └─ 判定：数据漂移 > 5°C，稳定性 < 95%
   ├─ 线缆连接故障
   │  ├─ 特征：信号强度、连接稳定性、传输质量
   │  ├─ 算法：连接诊断、信号质量分析
   │  └─ 判定：信号强度 < 80%，连接不稳定
   ├─ 数据采集故障
   │  ├─ 特征：数据完整性、采集频率、系统状态
   │  ├─ 算法：数据质量评估、系统诊断
   │  └─ 判定：数据完整性 < 90%，采集异常
   ├─ 通信系统故障
   │  ├─ 特征：通信延迟、数据丢包、网络状态
   │  ├─ 算法：网络性能分析、通信质量评估
   │  └─ 判定：延迟 > 5秒，丢包率 > 5%
   └─ 校准偏差问题
      ├─ 特征：校准偏差、测量精度、标准对比
      ├─ 算法：校准精度分析、偏差检测
      └─ 判定：偏差 > 2°C，精度下降 > 10%

6. 新粮后熟过程分析（新增）
   ├─ 正常生理活动
   │  ├─ 特征：入仓时间、温升模式、生理指标
   │  ├─ 算法：新粮模式识别、生理过程建模
   │  └─ 判定：入仓 < 30天，温升符合生理模式
   ├─ 水分平衡调整
   │  ├─ 特征：水分变化、平衡过程、调整速率
   │  ├─ 算法：水分平衡建模、调整过程分析
   │  └─ 判定：水分缓慢下降，平衡趋势明显
   ├─ 呼吸强度变化
   │  ├─ 特征：呼吸强度、CO2变化、代谢活动
   │  ├─ 算法：呼吸强度建模、代谢分析
   │  └─ 判定：呼吸强度逐渐降低，代谢趋于稳定
   ├─ 温度自然调节
   │  ├─ 特征：温度调节模式、自然降温、稳定趋势
   │  ├─ 算法：温度调节建模、稳定性分析
   │  └─ 判定：温度逐渐稳定，调节模式正常
   └─ 品质稳定过程
      ├─ 特征：品质指标变化、稳定性趋势
      ├─ 算法：品质稳定性建模、趋势分析
      └─ 判定：品质指标趋于稳定，无恶化趋势
```

#### 3.1.4 模块四：智能建议生成器（重构）
**生成策略**：
```
双重建议机制：
├─ 治标建议：基于异常现象的应急处理
│  ├─ 表层温度异常 → 表层通风、温度监控
│  ├─ 温升速率异常 → 立即通风、降温措施
│  └─ 分布异常 → 调整通风策略
└─ 治本建议：基于根本原因的根治措施
   ├─ 环境因素 → 环境控制、隔热改善
   ├─ 品质问题 → 品质检测、处理措施
   ├─ 设备故障 → 设备维修、更换计划
   ├─ 操作问题 → 流程优化、培训计划
   ├─ 硬件故障 → 硬件检修、系统升级
   └─ 新粮后熟 → 耐心等待、适度调控
```

### 3.2 数据流设计
```
多源数据输入 → 特征工程引擎 → 异常检测模型 → 根本原因推理 → 建议生成
     ↓              ↓              ↓              ↓           ↓
   数据质量评估   特征重要性分析   异常置信度评估   原因置信度评估  建议优先级排序
     ↓              ↓              ↓              ↓           ↓
   质量报告       特征解释        异常报告        原因分析报告   执行建议
```

### 3.3 模块间接口设计
```python
# 异常检测模块输出接口
class AnomalyDetectionResult:
    anomaly_type: str           # 异常类型（14种之一）
    severity_level: float       # 严重程度 [0-1]
    confidence: float           # 检测置信度 [0-1]
    spatial_location: dict      # 空间位置信息
    temporal_info: dict         # 时间信息
    affected_area: str          # 影响范围描述
    key_features: dict          # 关键特征值

# 根本原因分析模块输出接口
class RootCauseAnalysisResult:
    primary_cause: str          # 主要原因类型
    cause_confidence: float     # 原因置信度 [0-1]
    contributing_factors: list  # 贡献因素列表
    causal_strength: float      # 因果关系强度
    temporal_correlation: dict  # 时间关联信息
    evidence_summary: dict      # 证据汇总

# 建议生成模块输出接口
class ActionRecommendation:
    immediate_actions: list     # 立即行动建议
    root_cause_actions: list    # 根本原因处理建议
    preventive_measures: list   # 预防措施建议
    priority_order: list        # 优先级排序
    expected_outcome: str       # 预期效果
    resource_requirements: dict # 资源需求
```
