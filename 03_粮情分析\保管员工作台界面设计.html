<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>粮情分析系统 - 保管员工作台</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: #f5f7fa;
            color: #333;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 15px 20px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        .header h1 {
            font-size: 1.8em;
            margin-bottom: 5px;
        }
        
        .header .subtitle {
            opacity: 0.9;
            font-size: 0.9em;
        }
        
        .quick-status {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 8px;
            padding: 12px 20px;
            margin: 20px;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }
        
        .quick-status .message {
            color: #856404;
            font-weight: 500;
        }
        
        .quick-status .btn {
            background: #fd79a8;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 0.9em;
        }
        
        .silo-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 20px;
            padding: 20px;
        }
        
        .silo-card {
            background: white;
            border-radius: 12px;
            padding: 20px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
            cursor: pointer;
            border: 2px solid transparent;
        }
        
        .silo-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        }
        
        .silo-card.selected {
            border-color: #667eea;
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
        }
        
        .silo-card.normal {
            border-left: 4px solid #00b894;
        }
        
        .silo-card.warning {
            border-left: 4px solid #fdcb6e;
            background: #fffbf0;
        }
        
        .silo-card.danger {
            border-left: 4px solid #e17055;
            background: #fff5f5;
        }
        
        .silo-card.critical {
            border-left: 4px solid #d63031;
            background: #fff0f0;
            animation: pulse 2s infinite;
        }
        
        @keyframes pulse {
            0% { box-shadow: 0 4px 12px rgba(0,0,0,0.1); }
            50% { box-shadow: 0 8px 25px rgba(214, 48, 49, 0.3); }
            100% { box-shadow: 0 4px 12px rgba(0,0,0,0.1); }
        }
        
        .silo-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }
        
        .silo-name {
            font-size: 1.3em;
            font-weight: bold;
            display: flex;
            align-items: center;
        }
        
        .silo-icon {
            font-size: 1.5em;
            margin-right: 8px;
        }
        
        .status-tag {
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 0.8em;
            font-weight: 500;
        }
        
        .status-tag.normal { background: #00b894; color: white; }
        .status-tag.warning { background: #fdcb6e; color: #333; }
        .status-tag.danger { background: #e17055; color: white; }
        .status-tag.critical { background: #d63031; color: white; }
        
        .silo-metrics {
            margin: 15px 0;
        }
        
        .metric {
            display: flex;
            justify-content: space-between;
            margin: 8px 0;
            padding: 8px 0;
            border-bottom: 1px solid #eee;
        }
        
        .metric:last-child {
            border-bottom: none;
        }
        
        .metric .label {
            color: #666;
            font-size: 0.9em;
        }
        
        .metric .value {
            font-weight: bold;
        }
        
        .metric .value.danger {
            color: #d63031;
        }
        
        .alert-count {
            background: #ff7675;
            color: white;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 0.8em;
            display: inline-block;
            margin-top: 8px;
        }
        
        .last-update {
            color: #999;
            font-size: 0.8em;
            margin-top: 10px;
        }
        
        .view-detail-btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 6px;
            cursor: pointer;
            width: 100%;
            margin-top: 12px;
            font-size: 0.9em;
            transition: all 0.3s ease;
        }
        
        .view-detail-btn:hover {
            transform: scale(1.02);
        }
        
        .detail-panel {
            background: white;
            margin: 20px;
            border-radius: 12px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .detail-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            font-size: 1.2em;
            font-weight: bold;
        }
        
        .detail-tabs {
            display: flex;
            background: #f8f9fa;
            border-bottom: 1px solid #dee2e6;
        }
        
        .tab {
            padding: 15px 20px;
            cursor: pointer;
            border-bottom: 3px solid transparent;
            transition: all 0.3s ease;
            flex: 1;
            text-align: center;
            font-weight: 500;
        }
        
        .tab:hover {
            background: #e9ecef;
        }
        
        .tab.active {
            background: white;
            border-bottom-color: #667eea;
            color: #667eea;
        }
        
        .tab-content {
            padding: 20px;
            min-height: 400px;
        }
        
        .diagnosis-item {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 15px;
            border-left: 4px solid #e17055;
        }
        
        .diagnosis-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }
        
        .diagnosis-type {
            font-size: 1.1em;
            font-weight: bold;
            color: #e17055;
        }
        
        .confidence {
            background: #667eea;
            color: white;
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 0.8em;
        }
        
        .diagnosis-content {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }
        
        .feature-list {
            background: white;
            padding: 15px;
            border-radius: 6px;
        }
        
        .feature-item {
            display: flex;
            justify-content: space-between;
            padding: 8px 0;
            border-bottom: 1px solid #eee;
        }
        
        .feature-item:last-child {
            border-bottom: none;
        }
        
        .feature-value.exceeded {
            color: #d63031;
            font-weight: bold;
        }
        
        .suggestion-card {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 15px;
        }
        
        .suggestion-text {
            margin-bottom: 15px;
            line-height: 1.6;
        }
        
        .suggestion-actions {
            display: flex;
            gap: 10px;
        }
        
        .btn-primary {
            background: #00b894;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 6px;
            cursor: pointer;
        }
        
        .btn-secondary {
            background: #74b9ff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 6px;
            cursor: pointer;
        }
        
        .temperature-grid {
            display: grid;
            grid-template-columns: repeat(10, 1fr);
            gap: 2px;
            margin: 20px 0;
        }
        
        .temp-cell {
            aspect-ratio: 1;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 0.7em;
            font-weight: bold;
            border-radius: 2px;
            color: white;
            text-shadow: 1px 1px 1px rgba(0,0,0,0.5);
        }
        
        .hidden {
            display: none;
        }
        
        @media (max-width: 768px) {
            .silo-grid {
                grid-template-columns: 1fr;
                padding: 10px;
            }
            
            .detail-tabs {
                flex-direction: column;
            }
            
            .diagnosis-content {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🌾 粮情分析系统 - 保管员工作台</h1>
        <div class="subtitle">实时监控 | 智能诊断 | 科学决策</div>
    </div>

    <div class="quick-status">
        <div class="message">
            ⚠️ 当前有 3 个仓房需要立即关注：C仓(紧急)、B仓(危险)、D仓(警告)
        </div>
        <button class="btn" onclick="scrollToAbnormal()">立即查看</button>
    </div>

    <div class="silo-grid" id="siloGrid">
        <!-- 仓房状态卡片将通过JavaScript动态生成 -->
    </div>

    <div class="detail-panel hidden" id="detailPanel">
        <div class="detail-header" id="detailHeader">
            A仓 - 详细诊断报告
        </div>
        <div class="detail-tabs">
            <div class="tab active" onclick="switchTab('diagnosis')">🔍 AI诊断结果</div>
            <div class="tab" onclick="switchTab('suggestions')">📋 作业建议</div>
            <div class="tab" onclick="switchTab('evidence')">📊 判定依据</div>
            <div class="tab" onclick="switchTab('temperature')">🌡️ 粮温阵列</div>
        </div>
        <div class="tab-content" id="tabContent">
            <!-- 标签页内容将通过JavaScript动态生成 -->
        </div>
    </div>

    <script>
        // 模拟仓房数据
        const siloData = [
            {
                id: 'A',
                name: 'A仓',
                status: 'normal',
                maxTemp: 18.5,
                avgTemp: 16.2,
                alertCount: 0,
                lastUpdate: '2024-01-15 15:30:00',
                icon: '✅'
            },
            {
                id: 'B',
                name: 'B仓',
                status: 'danger',
                maxTemp: 28.3,
                avgTemp: 24.1,
                alertCount: 2,
                lastUpdate: '2024-01-15 15:29:00',
                icon: '⚠️'
            },
            {
                id: 'C',
                name: 'C仓',
                status: 'critical',
                maxTemp: 32.8,
                avgTemp: 29.5,
                alertCount: 4,
                lastUpdate: '2024-01-15 15:28:00',
                icon: '🚨'
            },
            {
                id: 'D',
                name: 'D仓',
                status: 'warning',
                maxTemp: 22.1,
                avgTemp: 19.8,
                alertCount: 1,
                lastUpdate: '2024-01-15 15:31:00',
                icon: '⚡'
            },
            {
                id: 'E',
                name: 'E仓',
                status: 'normal',
                maxTemp: 17.9,
                avgTemp: 15.8,
                alertCount: 0,
                lastUpdate: '2024-01-15 15:30:00',
                icon: '✅'
            },
            {
                id: 'F',
                name: 'F仓',
                status: 'normal',
                maxTemp: 19.2,
                avgTemp: 17.1,
                alertCount: 0,
                lastUpdate: '2024-01-15 15:29:00',
                icon: '✅'
            }
        ];

        let selectedSilo = null;

        // 生成仓房卡片
        function generateSiloCards() {
            const grid = document.getElementById('siloGrid');
            grid.innerHTML = '';

            siloData.forEach(silo => {
                const card = document.createElement('div');
                card.className = `silo-card ${silo.status}`;
                card.onclick = () => selectSilo(silo.id);

                card.innerHTML = `
                    <div class="silo-header">
                        <div class="silo-name">
                            <span class="silo-icon">${silo.icon}</span>
                            ${silo.name}
                        </div>
                        <span class="status-tag ${silo.status}">
                            ${getStatusText(silo.status)}
                        </span>
                    </div>
                    <div class="silo-metrics">
                        <div class="metric">
                            <span class="label">最高温度:</span>
                            <span class="value ${silo.maxTemp > 30 ? 'danger' : ''}">${silo.maxTemp}°C</span>
                        </div>
                        <div class="metric">
                            <span class="label">平均温度:</span>
                            <span class="value">${silo.avgTemp}°C</span>
                        </div>
                        ${silo.alertCount > 0 ? `
                            <div class="alert-count">
                                ${silo.alertCount} 个异常项目
                            </div>
                        ` : ''}
                    </div>
                    <div class="last-update">
                        更新时间: ${silo.lastUpdate}
                    </div>
                    ${silo.status !== 'normal' ? `
                        <button class="view-detail-btn">
                            👁️ 查看详细诊断
                        </button>
                    ` : ''}
                `;

                grid.appendChild(card);
            });
        }

        function getStatusText(status) {
            const statusMap = {
                'normal': '正常',
                'warning': '警告',
                'danger': '危险',
                'critical': '紧急'
            };
            return statusMap[status] || '未知';
        }

        function selectSilo(siloId) {
            selectedSilo = siloId;
            
            // 更新卡片选中状态
            document.querySelectorAll('.silo-card').forEach(card => {
                card.classList.remove('selected');
            });
            event.currentTarget.classList.add('selected');

            // 显示详细面板
            showDetailPanel(siloId);
        }

        function showDetailPanel(siloId) {
            const panel = document.getElementById('detailPanel');
            const header = document.getElementById('detailHeader');
            
            header.textContent = `${siloId}仓 - 详细诊断报告`;
            panel.classList.remove('hidden');
            
            // 默认显示诊断结果
            switchTab('diagnosis');
            
            // 滚动到详细面板
            panel.scrollIntoView({ behavior: 'smooth' });
        }

        function switchTab(tabName) {
            // 更新标签页状态
            document.querySelectorAll('.tab').forEach(tab => {
                tab.classList.remove('active');
            });
            event.target.classList.add('active');

            // 更新内容
            const content = document.getElementById('tabContent');
            content.innerHTML = getTabContent(tabName);
        }

        function getTabContent(tabName) {
            switch(tabName) {
                case 'diagnosis':
                    return getDiagnosisContent();
                case 'suggestions':
                    return getSuggestionsContent();
                case 'evidence':
                    return getEvidenceContent();
                case 'temperature':
                    return getTemperatureContent();
                default:
                    return '<p>内容加载中...</p>';
            }
        }

        function getDiagnosisContent() {
            return `
                <div class="diagnosis-item">
                    <div class="diagnosis-header">
                        <div class="diagnosis-type">表层局部发热</div>
                        <div class="confidence">AI置信度: 87.3%</div>
                    </div>
                    <div class="diagnosis-content">
                        <div>
                            <h4>异常描述</h4>
                            <p>检测到${selectedSilo}仓表层区域温度异常升高，存在明显的局部发热现象，可能影响粮食储存安全。</p>
                            <br>
                            <h4>影响区域</h4>
                            <span style="background: #e17055; color: white; padding: 4px 8px; border-radius: 4px;">表层东北角区域</span>
                        </div>
                        <div class="feature-list">
                            <h4>关键特征指标</h4>
                            <div class="feature-item">
                                <span>区域最高温度:</span>
                                <span class="feature-value exceeded">32.8°C ⚠️ 超阈值</span>
                            </div>
                            <div class="feature-item">
                                <span>区域温度标准差:</span>
                                <span class="feature-value exceeded">4.2°C ⚠️ 超阈值</span>
                            </div>
                            <div class="feature-item">
                                <span>24小时温升:</span>
                                <span class="feature-value exceeded">3.1°C ⚠️ 超阈值</span>
                            </div>
                            <div class="feature-item">
                                <span>邻域温差:</span>
                                <span class="feature-value exceeded">5.8°C ⚠️ 超阈值</span>
                            </div>
                        </div>
                    </div>
                </div>
            `;
        }

        function getSuggestionsContent() {
            return `
                <div class="suggestion-card">
                    <h4 style="color: #e17055; margin-bottom: 10px;">💡 表层局部发热 - 处理建议</h4>
                    <div class="suggestion-text">
                        <strong>立即执行：</strong><br>
                        1. 检查仓顶密封性，确认东北角区域有无渗漏<br>
                        2. 进仓进行感官检查，观察粮面有无异味、霉变、结块或虫迹<br>
                        3. 如情况轻微，启动仓顶风机进行水平通风<br>
                        4. 如发现严重问题，立即准备局部扒粮处理方案
                    </div>
                    <div class="suggestion-actions">
                        <button class="btn-primary" onclick="executeSuggestion()">✅ 执行建议</button>
                        <button class="btn-secondary">📋 查看详细步骤</button>
                        <button class="btn-secondary">📝 记录执行结果</button>
                    </div>
                </div>
                <div class="suggestion-card">
                    <h4 style="color: #fdcb6e; margin-bottom: 10px;">⚡ 预防措施建议</h4>
                    <div class="suggestion-text">
                        <strong>后续预防：</strong><br>
                        1. 加强该区域的日常巡检频次<br>
                        2. 在下次空仓期检修仓顶防水层<br>
                        3. 考虑在该区域增设温湿度监测点<br>
                        4. 建立该区域的专项监控档案
                    </div>
                    <div class="suggestion-actions">
                        <button class="btn-secondary">📅 制定预防计划</button>
                    </div>
                </div>
            `;
        }

        function getEvidenceContent() {
            return `
                <h4>特征数据分析</h4>
                <table style="width: 100%; border-collapse: collapse; margin: 20px 0;">
                    <thead>
                        <tr style="background: #f8f9fa;">
                            <th style="padding: 12px; border: 1px solid #dee2e6;">特征名称</th>
                            <th style="padding: 12px; border: 1px solid #dee2e6;">当前值</th>
                            <th style="padding: 12px; border: 1px solid #dee2e6;">正常阈值</th>
                            <th style="padding: 12px; border: 1px solid #dee2e6;">状态</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td style="padding: 12px; border: 1px solid #dee2e6;">表层最高温度</td>
                            <td style="padding: 12px; border: 1px solid #dee2e6; color: #d63031; font-weight: bold;">32.8°C</td>
                            <td style="padding: 12px; border: 1px solid #dee2e6;">< 25°C</td>
                            <td style="padding: 12px; border: 1px solid #dee2e6;"><span style="background: #d63031; color: white; padding: 4px 8px; border-radius: 4px;">异常</span></td>
                        </tr>
                        <tr>
                            <td style="padding: 12px; border: 1px solid #dee2e6;">区域温度标准差</td>
                            <td style="padding: 12px; border: 1px solid #dee2e6; color: #d63031; font-weight: bold;">4.2°C</td>
                            <td style="padding: 12px; border: 1px solid #dee2e6;">< 2.0°C</td>
                            <td style="padding: 12px; border: 1px solid #dee2e6;"><span style="background: #d63031; color: white; padding: 4px 8px; border-radius: 4px;">异常</span></td>
                        </tr>
                        <tr>
                            <td style="padding: 12px; border: 1px solid #dee2e6;">24小时温升速率</td>
                            <td style="padding: 12px; border: 1px solid #dee2e6; color: #d63031; font-weight: bold;">3.1°C</td>
                            <td style="padding: 12px; border: 1px solid #dee2e6;">< 1.0°C</td>
                            <td style="padding: 12px; border: 1px solid #dee2e6;"><span style="background: #d63031; color: white; padding: 4px 8px; border-radius: 4px;">异常</span></td>
                        </tr>
                        <tr>
                            <td style="padding: 12px; border: 1px solid #dee2e6;">邻域温差</td>
                            <td style="padding: 12px; border: 1px solid #dee2e6; color: #d63031; font-weight: bold;">5.8°C</td>
                            <td style="padding: 12px; border: 1px solid #dee2e6;">< 3.0°C</td>
                            <td style="padding: 12px; border: 1px solid #dee2e6;"><span style="background: #d63031; color: white; padding: 4px 8px; border-radius: 4px;">异常</span></td>
                        </tr>
                    </tbody>
                </table>
                
                <h4 style="margin-top: 30px;">AI决策路径</h4>
                <div style="margin: 20px 0;">
                    <div style="display: flex; align-items: center; margin: 10px 0;">
                        <div style="width: 20px; height: 20px; background: #74b9ff; border-radius: 50%; margin-right: 15px;"></div>
                        <span>数据采集: 获取${selectedSilo}仓所有传感器数据</span>
                    </div>
                    <div style="display: flex; align-items: center; margin: 10px 0;">
                        <div style="width: 20px; height: 20px; background: #fdcb6e; border-radius: 50%; margin-right: 15px;"></div>
                        <span>特征计算: 计算表层区域多维度特征</span>
                    </div>
                    <div style="display: flex; align-items: center; margin: 10px 0;">
                        <div style="width: 20px; height: 20px; background: #e17055; border-radius: 50%; margin-right: 15px;"></div>
                        <span>异常检测: 发现表层局部发热模式</span>
                    </div>
                    <div style="display: flex; align-items: center; margin: 10px 0;">
                        <div style="width: 20px; height: 20px; background: #00b894; border-radius: 50%; margin-right: 15px;"></div>
                        <span>置信度评估: 87.3% (高置信度)</span>
                    </div>
                </div>
            `;
        }

        function getTemperatureContent() {
            return `
                <div style="margin-bottom: 20px;">
                    <button class="btn-primary" style="margin-right: 10px;">🔥 热力图</button>
                    <button class="btn-secondary" style="margin-right: 10px;">📊 数据表</button>
                    <button class="btn-secondary">🎯 3D视图</button>
                </div>
                
                <h4>${selectedSilo}仓粮温分布阵列 (°C)</h4>
                <div class="temperature-grid">
                    ${generateTemperatureGrid()}
                </div>
                
                <h4 style="margin-top: 30px;">异常点位标注</h4>
                <div style="margin: 15px 0;">
                    <span style="background: #d63031; color: white; padding: 4px 8px; border-radius: 4px; margin: 4px;">东北角(8,2) - 32.8°C</span>
                    <span style="background: #e17055; color: white; padding: 4px 8px; border-radius: 4px; margin: 4px;">东北角(7,2) - 30.1°C</span>
                    <span style="background: #e17055; color: white; padding: 4px 8px; border-radius: 4px; margin: 4px;">东北角(8,1) - 29.8°C</span>
                </div>
                
                <div style="margin-top: 20px; padding: 15px; background: #f8f9fa; border-radius: 8px;">
                    <h4>温度分析说明</h4>
                    <p>• 红色区域(>30°C): 需要立即关注的高温区域</p>
                    <p>• 橙色区域(25-30°C): 需要监控的温升区域</p>
                    <p>• 黄色区域(20-25°C): 正常偏高温度区域</p>
                    <p>• 绿色区域(<20°C): 正常温度区域</p>
                </div>
            `;
        }

        function generateTemperatureGrid() {
            const temps = [];
            for (let i = 0; i < 100; i++) {
                let temp;
                // 模拟东北角高温区域
                if ((i >= 17 && i <= 19) || (i >= 27 && i <= 29) || (i >= 37 && i <= 39)) {
                    temp = 28 + Math.random() * 5; // 28-33°C
                } else {
                    temp = 15 + Math.random() * 8; // 15-23°C
                }
                temps.push(temp.toFixed(1));
            }
            
            return temps.map(temp => {
                const t = parseFloat(temp);
                let bgColor;
                if (t > 30) bgColor = '#d63031';
                else if (t > 25) bgColor = '#e17055';
                else if (t > 20) bgColor = '#fdcb6e';
                else bgColor = '#00b894';
                
                return `<div class="temp-cell" style="background-color: ${bgColor}">${temp}</div>`;
            }).join('');
        }

        function executeSuggestion() {
            alert('建议已标记为执行中，请按照步骤进行操作，并及时记录执行结果。');
        }

        function scrollToAbnormal() {
            const abnormalCards = document.querySelectorAll('.silo-card.danger, .silo-card.critical, .silo-card.warning');
            if (abnormalCards.length > 0) {
                abnormalCards[0].scrollIntoView({ behavior: 'smooth' });
                abnormalCards[0].click();
            }
        }

        // 初始化页面
        generateSiloCards();

        // 模拟实时数据更新
        setInterval(() => {
            siloData.forEach(silo => {
                if (silo.status !== 'normal') {
                    silo.maxTemp = (silo.maxTemp + (Math.random() - 0.5) * 0.2).toFixed(1);
                    silo.avgTemp = (silo.avgTemp + (Math.random() - 0.5) * 0.1).toFixed(1);
                }
            });
            generateSiloCards();
        }, 10000); // 每10秒更新一次
    </script>
</body>
</html>
