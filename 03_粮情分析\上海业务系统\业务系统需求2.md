# 粮食入库全流程流程图及关键节点说明  


## 一、入库流程总览（附流程图框架）  

```mermaid
graph TD
    A[计划制定] --> B[来粮预报]
    B --> C[船舶/汽车到达确认]
    C --> D[扦样检验]
    D --> E{检验合格?}
    E -->|是| F[检斤确认]
    E -->|否| G[退回/重新检验]
    F --> H[入库作业单生成]
    H --> I[商品粮入库]
    I --> J[整仓第三方检验]
    J --> K{验收合格?}
    K -->|是| L[性质转换单生效]
    K -->|否| M[整仓处理/复检]
    L --> N[转为储备粮]
```  


## 二、关键节点详细说明  

### 1. **计划制定与红线管理**  

- **储备业务部核心职责**：  
  - 根据南局年度计划制定轮换计划，需满足 **规模红线**（总库存≥200万吨×80%）和 **区域红线**（市内库存≥50%）[1-1]。  
  - 动态监控库存是否达标，例如“若当前库存150万吨，需触发预警并启动采购”[1-1][1-6]。  

### 2. **来粮预报与合同签订**  

- **购销中心主导流程**：  
  - 客户需向购销中心提交 **来粮预报**（而非直接联系粮库），生成唯一通知单号[1-4][1-5]。  
  - 预报后通过网上竞拍签订购销合同，明确粮食品种、数量、价格等关键信息[1-4]。  

### 3. **到达确认与关联校验**  

- **船舶/汽车到港后操作**：  
  - 必须关联对应预报单（通过通知单号匹配），避免重复到港导致数据混乱[1-5]。  
  - 例如：“某船舶第3次到港，需手动选择对应预报单编号，确保与前期来粮预报关联”[1-5]。  

### 4. **扦样检验与代储点管理**  

- **检验分工**：  
  - **直属库**：负责船运粮食扦样检验（如水分、杂质、重金属指标）[16-0][29-0]。  
  - **代储点**：汽车运粮需自检并上传数据，直属库需二次确认检验结果[1-5][29-0]。  
- **关键风险点**：代储点数据需人工确认后才生效，未确认数据可能被后续修改冲销[1-5]。  

### 5. **检斤确认与双账并行**  

- **检斤系统操作**：  
  - 称重后需 **人工确认数据**，防止后续称重记录覆盖当前结果[1-5][1-6]。  
  - 数据同步至 **实物账**（保管员视角）和 **统计账**（上报粮食局），二者需定期对账[1-6][5-0]。  

### 6. **性质转换与储备粮认定**  

- **商品粮→储备粮关键节点**：  
  - 入库后粮食默认状态为 **商品粮**，需完成 **整仓第三方检验**（通常滞后2周）[1-0][1-3]。  
  - 检验合格后通过 **性质转换单** 手动触发状态变更，系统标记为储备粮[1-0][1-6]。  
- **特殊场景**：若整仓检验不合格（如黄曲霉超标），需整仓清理后重新入库[16-0][29-0]。  


## 三、系统对接与数据流转  

### 1. **跨部门数据交互**  

- **购销中心→沪粮公司**：来粮预报单需经沪粮公司转发至库点，无转发则库点无法接收指令[1-4]。  
- **代储点→直属库**：代储点检斤数据需直属库确认后才计入有效库存，避免数据篡改风险[1-5][1-6]。  

### 2. **监控与粮温管理**  

- **实时监控对接**：库区监控需接入集团统一平台，档案系统不直接展示监控画面[1-2][6-0]。  
- **粮温数据**：代储点粮温需通过标准化接口同步至集团系统，历史数据不存储于本地[1-2][6-0]。  


## 四、待补充细节建议  

1. **代储点数据延迟问题**：需明确“代储点自检数据上传至直属库确认”的最长时效（如24小时内）。  
2. **第三方检验周期**：建议补充“整仓检验从申请到出结果的标准时长”（如10个工作日）。  
3. **异常处理流程**：例如“船舶到港后无法关联预报单”的应急操作步骤。  

> 可结合实际业务需求，基于此流程图进一步细化岗位分工（如购销中心/沪粮公司职责边界）或系统字段（如性质转换单状态标识）[1-4][1-6]。