**特征工程 (Feature Engineering).xlsx**

**Sheet: 静态特征**

| 特征名 (Field Name)          | 描述                      | 数据类型        | 备注                                                                                                                                                                                      |
|:--------------------------|:------------------------|:------------|:----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|
| entity_id/sensor_id       | 实体ID/测温点ID (唯一标识)       | Categorical | 模型不只是预测单个测温点"传感器"的工具。将预测对象的概念扩大到"实体"，把"某个聚合层/圈"和"整个粮堆"也视作可以被预测的独立实体。在数据层面创建这些聚合实体，并为它们设计有意义的"伪静态特征"，然后将它们与真实的传感器数据"一视同仁"地送入同一个TFT模型进行训练。例如：sensor_001, silo_A_layer_1, silo_A_total_avg |
| entity_type               | 类别特征                    | Categorical | 标识实体类型（如 'sensor', 'layer_aggregate', 'silo_aggregate'）                                                                                                                                |
| aggregation_level         | 数字特征                    | Categorical | 表示层级（0: sensor, 1: layer/ring, 2: silo）                                                                                                                                                |
| silo_id                   | 所在仓房ID                  | Categorical | 标识属于哪个仓房                                                                                                                                                                               |
| silo_type                 | 仓房类型                    | Categorical | 平房仓, 立筒仓,浅圆仓（"flat", "silo", "shallow_round"）                                                                                                                                          |
| silo_volume_m3            | 仓房体积 (m³)               | Numeric     |                                                                                                                                                                                        |
| insulation_thickness_mm   | 保温层厚度 (mm)              | Numeric     |                                                                                                                                                                                        |
| wall_material             | 墙体材质                    | Categorical | 混凝土/砖/钢板（"concrete", "brick", "steel"）                                                                                                                                                 |
| roof_material             | 屋顶材质                    | Categorical | 金属/瓦/混凝土（"metal", "tile", "concrete"）                                                                                                                                                  |
| grain_type                | 粮食品种                    | Categorical | 小麦/玉米/稻谷（"wheat", "maize", "rice"）                                                                                                                                                     |
| bulk_density_kg_m3        | 粮食品种容重 (kg/m³)          | Numeric     | 可用标准值                                                                                                                                                                                  |
| specific_heat_j_kgk       | 比热容 (J/(kg·K))          | Numeric     | 可用标准值                                                                                                                                                                                  |
| thermal_conductivity_w_mk | 导热系数 (W/(m·K))          | Numeric     | 可用标准值                                                                                                                                                                                  |
| grain_pile_height_m       | 粮堆高度 (m)                | Numeric     |                                                                                                                                                                                        |
| grain_pile_volume_m3      | 粮堆体积（m³）                | Numeric     |                                                                                                                                                                                        |
| vent_power_kw             | 风机功率 (kW)               | Numeric     |                                                                                                                                                                                        |
| ac_capacity_kw            | 空调制冷能力 (kW)             | Numeric     |                                                                                                                                                                                        |
| district                  | 所属行政区域                  | Categorical |                                                                                                                                                                                        |
| region_climate_zone       | 地区气候带                   | Categorical |                                                                                                                                                                                        |
| altitude_m                | 海拔 (m)                  | Numeric     |                                                                                                                                                                                        |
| avg_sensor_pos_x          | 实体内所有传感器位置X坐标的平均坐标      | Numeric     | 宏观中心 (Macro Centroid):单个传感器时为sensor_pos_x的值。这组特征描述了实体中心位置的特征，给模型一个关于该聚合实体宏观位置的提示，需要使用边界框坐标系（所有坐标非负）。                                                                           |
| avg_sensor_pos_y          | 实体内所有传感器位置Y坐标的平均坐标      | Numeric     | 宏观中心 (Macro Centroid):单个传感器时为sensor_pos_y的值。                                                                                                                                           |
| avg_sensor_pos_z          | 实体内所有传感器位置Z坐标 (高度)的平均坐标 | Numeric     | 宏观中心 (Macro Centroid):单个传感器时为sensor_pos_z的值。                                                                                                                                           |
| extent_x                  | x维度的空间延展性               |             | 空间延展性 (Spatial Extent)：extent_x = max(sensor_pos_x) - min(sensor_pos_x)。这组特征描述了该实体内所有传感器的最小边界框的尺寸。它直接告诉模型这个实体在三个维度上"有多大"。例如，一个"层"的 extent_z 会很小，而 extent_x 和 extent_y 会很大。一个"垂直电缆"的 extent_x 和 extent_y 会很小，而 extent_z 会很大。这组特征与坐标系原点无关，具有平移不变性，非常鲁棒。 |
| extent_y                  | y维度的空间延展性               |             | 空间延展性 (Spatial Extent)：extent_y = max(pos_y) - min(pos_y)                                                                                                                               |
| extent_y                  | z维度的空间延展性               |             | 空间延展性 (Spatial Extent)：extent_z = max(pos_z) - min(pos_z)                                                                                                                               |
| num_sensors_in_group      | 该实体包含的传感器数量             | Numeric     | 传感器密度/数量 (Sensor Count/Density):entity_type=sensor时即原始传感器数据时该值为1。可用该特征区分一个由10个传感器组成的"层"和一个由2个传感器组成的稀疏"层"。通过 avg_sensor_pos_*、 extent_*和num_sensors_in_group共同描述聚合实体的中心、范围和密度相关的空间特征。                                                                              |

**Sheet: 历史观测特征**

| 特征名 (Field Name)       | 描述              | 数据类型         | 备注                         |
|:-----------------------|:----------------|:-------------|:---------------------------|
| grain_temp_c           | 测温点粮温 (°C)      | Numeric      | 主要观测变量，也是预测目标的前身           |
| grain_rh_pct           | 测温点相对湿度 (%)     | Numeric      |                            |
| grain_moisture_pct     | 粮堆平均水分 (%)      | Numeric      | 若是低频测量，需前向/线性插值到与粮温同样的时间频率 |
| silo_interior_temp_c   | 仓内空气温度 (°C)     | Numeric      |                            |
| silo_interior_rh_pct   | 仓内相对湿度 (%)      | Numeric      |                            |
| outdoor_temp_c         | 室外温度 (°C)       | Numeric      |                            |
| outdoor_rh_pct         | 室外相对湿度 (%)      | Numeric      |                            |
| solar_radiation_w_m2   | 太阳总辐射 (W/m²)    | Numeric      | 对屋顶和墙壁温度影响显著               |
| ventilation_status     | 通风开关状态          | Binary (0/1) | 历史操作记录                     |
| ventilation_duration_h | 当日累计通风时长 (h)    | Numeric      |                            |
| ac_status              | 空调开关状态          | Binary (0/1) | 历史操作记录                     |
| ac_duration_h          | 当日累计制冷时长 (h)    | Numeric      |                            |
| time_idx               | 一个从0开始递增的整数时间索引 | Numeric      |                            |
| day_of_year            | 年内日序            | Numeric      | 周期性特征                      |
| month                  | 月份              | Categorical  |                            |

**Sheet: 已知未来特征**

| 特征名 (Field Name)              | 描述            | 数据类型         | 备注        |
|:------------------------------|:--------------|:-------------|:----------|
| forecast_outdoor_temp_c       | 预报-室外温度 (°C)  | Numeric      | 来自天气预报    |
| forecast_outdoor_rh_pct       | 预报-室外相对湿度 (%) | Numeric      | 来自天气预报    |
| forecast_solar_radiation_w_m2 | 预报-太阳总辐射      | Numeric      | 来自天气预报    |
| plan_ventilation_status       | 计划-通风开关       | Binary (0/1) | 优化模块的关键输入 |
| plan_ac_status                | 计划-空调开关       | Binary (0/1) | 优化模块的关键输入 |
| day_of_year                   | 年内日序          | Numeric      | 周期性特征     |
| month                         | 月份            | Categorical  |           |
| is_holiday                    | 是否节假日         | Binary (0/1) |           |