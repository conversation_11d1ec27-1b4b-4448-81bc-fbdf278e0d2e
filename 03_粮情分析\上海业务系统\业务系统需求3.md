# 粮食公司业务架构与流程补充说明（修订版）  


## 一、核心业务部门分工细化  

### 1. **储备业务部**  

- **计划制定**：根据南局年度计划制定轮换计划，需满足 **规模红线**（总库存≥200万吨×80%）和 **区域红线**（市内库存≥50%、市外库存≤50%）。  
- **动态监控**：实时跟踪库存是否达标（如当前库存150万吨时触发采购预警），红线管理与粮库日常操作无直接关联。  

### 2. **购销中心**（原购销分公司，保留独立财务/业务架构）  

- **客户对接**：  
  - 接收客户 **来粮预报**（唯一通知单号）、提货指令，禁止客户直接联系粮库。  
  - 组织网上竞拍、签订购销合同，负责结算付款（含入库/出库金额确认）。  
- **指令转发**：来粮预报需经沪粮公司手动转发至库点（当前流程需人工操作，未来可优化为自动同步）。  

### 3. **沪粮公司**（子公司，原仓储公司，现称保管中心）  

- **粮库管理**：执行粮食进出库操作、保管作业，管理库区监控系统（实时画面需对接集团平台，不存储历史数据）。  
- **代储点协作**：汽车运粮由代储点自检，数据需直属库检验员二次确认；船运粮由直属库直接检验。  


## 二、粮食入库流程补充细节  

### 1. **到达确认与关联校验**  

- **多批次管理**：船舶/汽车可能多次到港，需通过通知单号关联对应来粮预报（例如“某船舶第3次到港，需手动选择预报单编号”）。  
- **排队机制**：到港后需排队等待卸货，由粮库安排作业顺序。  

### 2. **扦样检验与代储点分工**  

| 运输方式 | 检验责任方       | 数据流程                     |  
|----------|------------------|------------------------------|  
| 船运粮   | 直属库检验员     | 扦样→检验→数据直接上传       |  
| 汽车粮   | 代储点自检       | 自检数据→直属库检验员确认生效 |  

### 3. **检斤确认与双账管理**  

- **检斤操作**：称重后需 **统计员人工确认数据**，防止后续称重记录覆盖（要求日清月结，避免跨日修改）。  
- **双账并行规则**：  
  - **实物账**：保管员记录实际进出库量（含商品粮/储备粮），实时更新。  
  - **统计账**：用于上报粮食局，需通过 **驳运单** 确认后生效，可能滞后于实物账。  

### 4. **性质转换关键规则**  

- **商品粮阶段**：入库后未验收前均为商品粮，计入实物账但不计入储备库存。  
- **储备粮转换**：整仓第三方检验合格后（通常滞后2周），由储备业务部生成 **性质转换单**（手动操作），系统标记为储备粮并同步至统计账。  
- **特殊场景**：若整仓检验不合格（如黄曲霉超标），需整仓清理后重新入库。  


## 三、库存管理核心补充  

### 1. **统计账与实物账差异**  

| 维度       | 实物账（保管员视角）          | 统计账（上报粮食局）          |  
|------------|-------------------------------|-------------------------------|  
| 数据来源   | 检斤单确认后实时更新          | 需驳运单+性质转换单双重确认   |  
| 包含范围   | 商品粮+储备粮                 | 仅储备粮（验收合格后）        |  
| 修改限制   | 允许冲销调整（需权限）        | 禁止随意修改，需审批流程      |  

### 2. **代储点数据对接**  

- **监控系统**：实时画面直接访问集团平台，档案系统不存储历史数据。  
- **粮温管理**：需单独设计页面展示标准化点位数据（格式已按粮食局要求统一），代储点与直属库数据格式一致。  


## 四、系统对接与待确认事项  

### 1. **监控与粮温集成**  

- **实时监控**：通过集团平台接口访问，不本地存储；档案系统仅支持查看实时画面，不集成历史数据。  
- **粮温展示**：需独立页面展示代储点粮温数据，点位格式标准化（由达联公司完成对接）。  

### 2. **待确认细节**  

1. **成品粮档案粒度**：20+货位按“廒间”还是“单个货位”建档？  
2. **统计维度需求**：领导需在“库区智脑”查看的指标（如按品种/时间/区域统计）需进一步明确。  
3. **数据库字段**：需确认统计账/实物账的关键字段（如性质代码、状态标识）及性质转换单的触发条件。  

> 建议：优先基于现有数据库视图（含库存类型、性质代码）开发初版，数据准确后再迭代调整统计维度~ 📊  

需要针对某部分补充表格或流程示意图吗？