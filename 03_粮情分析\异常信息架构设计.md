# 单仓异常详情页面信息架构设计

## 1. 异常分类体系分析

### 1.1 异常类型分组
根据异常的性质、处理方式和紧急程度，将18种异常类型分为4个主要分组：

#### 硬件异常组 (Hardware Issues)
- **异常类型**：硬件异常-点位、硬件异常-线缆
- **共同特征**：数据可靠性问题，不影响粮食安全，但影响监控准确性
- **处理原则**：标记异常、安排检修、加强人工巡检
- **紧急程度**：低-中等
- **处理时间**：1-7天（空仓期处理）

#### 温度异常组 (Temperature Anomalies)
- **异常类型**：温升速率异常、超最高温、整仓发热
- **共同特征**：温度相关，可能影响粮食品质，需要通风或降温处理
- **处理原则**：立即通风、温度控制、品质检测
- **紧急程度**：中-高
- **处理时间**：立即-24小时

#### 空间异常组 (Spatial Anomalies)
- **异常类型**：表层局部/整体发热、中部局部/整体发热、底部局部/整体发热、垂直局部/整体发热
- **共同特征**：特定区域异常，可能存在局部问题，需要针对性处理
- **处理原则**：区域检查、局部处理、防止扩散
- **紧急程度**：中-高
- **处理时间**：立即-48小时

#### 分布异常组 (Distribution Anomalies)
- **异常类型**：同层/圈不均、外圈粮温过高、仓内温差过大、新粮入仓后熟
- **共同特征**：温度分布不均，需要调节通风或改善储存条件
- **处理原则**：优化通风、均衡温度、改善储存环境
- **紧急程度**：低-中等
- **处理时间**：1-72小时

### 1.2 统一展示模板设计

#### 异常组模板
```typescript
interface AnomalyGroup {
  groupId: string;
  groupName: string;
  groupType: 'hardware' | 'temperature' | 'spatial' | 'distribution';
  urgencyLevel: 'low' | 'medium' | 'high' | 'critical';
  anomalies: Anomaly[];
  commonCharacteristics: string;
  treatmentPrinciple: string;
  estimatedTime: string;
  requiredResources: string[];
}
```

#### 单项异常模板
```typescript
interface Anomaly {
  id: string;
  type: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
  confidence: number;
  affectedArea: string;
  description: string;
  evidenceData: {
    keyFeatures: FeatureData[];
    thresholdViolations: ThresholdViolation[];
    supportingCharts: ChartData[];
  };
  actionPlan: {
    immediateActions: Action[];
    followUpActions: Action[];
    preventiveMeasures: Action[];
  };
  expectedOutcome: string;
  estimatedDuration: string;
}
```

## 2. "总-分-据"层次化结构设计

### 第一层：异常总述
```typescript
interface AnomalySummary {
  siloId: string;
  totalAnomalies: number;
  anomalyBreakdown: {
    critical: number;
    high: number;
    medium: number;
    low: number;
  };
  overallUrgency: 'immediate' | 'urgent' | 'monitor' | 'routine';
  prioritizedActions: PriorityAction[];
  resourceRequirements: {
    personnel: string[];
    equipment: string[];
    estimatedTime: string;
    estimatedCost?: string;
  };
  riskAssessment: string;
}
```

### 第二层：异常分类展示
按照4个主要分组展示，每个分组包含：
- 分组概述和统计
- 该组异常的共同处理原则
- 组内异常的优先级排序
- 统一的处理时间线

### 第三层：单项异常详情
采用参数化模板，避免重复内容：
- 异常描述（参数化：区域、类型、程度）
- 判定依据（标准化特征展示）
- 作业建议（模板化步骤）
- 支撑数据（统一图表格式）

## 3. 参数化展示设计

### 3.1 区域参数化
对于不同区域的相似异常（如局部发热），使用参数化模板：

```typescript
interface RegionalAnomalyTemplate {
  baseType: 'local_heating' | 'overall_heating';
  region: 'surface' | 'middle' | 'bottom' | 'vertical';
  regionDisplayName: string;
  regionSpecificRisks: string[];
  regionSpecificActions: Action[];
}
```

### 3.2 严重程度参数化
根据严重程度调整展示内容：

```typescript
interface SeverityTemplate {
  level: 'low' | 'medium' | 'high' | 'critical';
  urgencyIndicator: string;
  responseTime: string;
  escalationCriteria: string;
  resourceLevel: 'minimal' | 'standard' | 'enhanced' | 'emergency';
}
```

## 4. 信息层次清晰性设计

### 4.1 视觉层次
- **第一层**：大标题、关键指标、紧急状态指示器
- **第二层**：分组卡片、统计图表、处理原则
- **第三层**：详细数据表格、具体操作步骤、支撑图表

### 4.2 交互层次
- **概览模式**：显示总述和分组概要
- **分组模式**：展开特定分组的详细信息
- **详情模式**：深入单项异常的完整论据

### 4.3 信息密度控制
- **高密度区域**：总述层，信息紧凑但清晰
- **中密度区域**：分组层，平衡概览和细节
- **低密度区域**：详情层，充分展示支撑数据

## 5. 实施要点

### 5.1 模板复用策略
- 建立异常类型映射表
- 设计可配置的展示组件
- 实现动态内容生成

### 5.2 数据组织策略
- 预处理异常数据，按分组组织
- 计算优先级和资源需求
- 生成标准化的展示数据

### 5.3 用户体验优化
- 提供快速导航功能
- 支持折叠/展开操作
- 实现智能推荐和提醒

这种层次化的信息架构设计确保保管员能够：
1. **3秒内**了解整体异常情况和紧急程度
2. **30秒内**掌握各类异常的处理优先级
3. **3分钟内**获得具体异常的完整处理方案
4. **随时验证**AI判断的依据和合理性
